{"127.0.0.1": {"213": {"metadata.fields": {"Name": "Sampleebs-env", "elasticbeanstalk:environment-id": "e-kypxnsq6kf", "elasticbeanstalk:environment-name": "Sampleebs-env"}}, "_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 176993700500105, "event.timestamp": 1655204190, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon Elastic BeansTalk", "metric.object": ***************, "metric.plugin": "awselasticbeanstalk", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "AWS Elastic Beanstalk", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.233 pm 14/06/2022", "object.custom.fields": {"***************": "Sampleebs-env", "***************": "e-kypxnsq6kf", "***************": "Sampleebs-env"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 21, "object.name": "Sampleebs-env(ap-south-1)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "Sampleebs-env(ap-south-1)", "object.type": "AWS Elastic Beanstalk", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 213, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"213": {"metadata.fields": {"Name": "Sampleebs-env", "elasticbeanstalk:environment-id": "e-kypxnsq6kf", "elasticbeanstalk:environment-name": "Sampleebs-env"}}, "aws.elasticbeanstalk.application.2xx.requests": 3, "aws.elasticbeanstalk.application.3xx.requests": 0, "aws.elasticbeanstalk.application.4xx.requests": 0, "aws.elasticbeanstalk.application.5xx.requests": 0, "aws.elasticbeanstalk.application.p10.latency.seconds": 0.001, "aws.elasticbeanstalk.application.p50.latency.seconds": 0.001, "aws.elasticbeanstalk.application.p75.latency.seconds": 0.002, "aws.elasticbeanstalk.application.p85.latency.seconds": 0.002, "aws.elasticbeanstalk.application.p90.latency.seconds": 0.002, "aws.elasticbeanstalk.application.p95.latency.seconds": 0.002, "aws.elasticbeanstalk.application.p99.latency.seconds": 0.002, "aws.elasticbeanstalk.application.p999.latency.seconds": 0.002, "aws.elasticbeanstalk.application.requests": 3, "aws.elasticbeanstalk.cpu.idle.percent": 99.8, "aws.elasticbeanstalk.cpu.io.wait.percent": 0, "aws.elasticbeanstalk.cpu.irq.percent": 0, "aws.elasticbeanstalk.cpu.nice.percent": 0, "aws.elasticbeanstalk.cpu.soft.irq.percent": 0, "aws.elasticbeanstalk.cpu.system.percent": 0.1, "aws.elasticbeanstalk.cpu.user.percent": 0.1, "aws.elasticbeanstalk.degraded.instances": 0, "aws.elasticbeanstalk.deploy.time": **********, "aws.elasticbeanstalk.environment.health": "Ok", "aws.elasticbeanstalk.info.instances": 0, "aws.elasticbeanstalk.instance.health": "Ok", "aws.elasticbeanstalk.launch.time": **********, "aws.elasticbeanstalk.load.avg1.min": 0, "aws.elasticbeanstalk.nodata.instances": 0, "aws.elasticbeanstalk.ok.instances": 1, "aws.elasticbeanstalk.pending.instances": 0, "aws.elasticbeanstalk.severe.instances": 0, "aws.elasticbeanstalk.unknown.instances": 0, "aws.elasticbeanstalk.warning.instances": 0}, "status": "succeed", "timeout": 60}}