{"************": {"errors": [], "object.ip": "************", "password": "Mind@123", "port": 5985, "result": {"dotnet.app": [{"dotnet.app": "_global_", "dotnet.app.appdomains": 2, "dotnet.app.assemblies": 38, "dotnet.app.ccws": 271, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 3, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 18, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 2.48, "dotnet.app.heap.bytes": 7461392, "dotnet.app.jit.bytes": 127921, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 2166, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 38, "dotnet.app.loaded.classes": 959, "dotnet.app.loader.heap.bytes": 4665344, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 8, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 6, "dotnet.app.pinned.objects": 11, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0.1, "dotnet.app.runtime.checks": 2294, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 465, "dotnet.app.stack.walk.depth": 2, "dotnet.app.stubs": 1476, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 18, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 4.96, "dotnet.app.heap.bytes": 3779312, "dotnet.app.jit.bytes": 3145, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 71, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 293, "dotnet.app.loader.heap.bytes": 1519616, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 3, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 2, "dotnet.app.pinned.objects": 8, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 1409, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 8, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 193, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "inetmgr", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 20, "dotnet.app.ccws": 267, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 0, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 3682080, "dotnet.app.jit.bytes": 124776, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 2095, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 20, "dotnet.app.loaded.classes": 666, "dotnet.app.loader.heap.bytes": 3145728, "dotnet.app.loading.failures": 0, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 5, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 4, "dotnet.app.pinned.objects": 3, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0.1, "dotnet.app.runtime.checks": 885, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 457, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 1283, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}]}, "timeout": 60, "username": "Administrator"}, "************": {"errors": [], "object.ip": "************", "password": "Mind@123", "port": 5985, "result": {"dotnet.app": [{"dotnet.app": "_global_", "dotnet.app.appdomains": 2, "dotnet.app.assemblies": 38, "dotnet.app.ccws": 271, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 3, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 18, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 2.48, "dotnet.app.heap.bytes": 7461392, "dotnet.app.jit.bytes": 127921, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 2166, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 38, "dotnet.app.loaded.classes": 959, "dotnet.app.loader.heap.bytes": 4665344, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 8, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 6, "dotnet.app.pinned.objects": 11, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0.1, "dotnet.app.runtime.checks": 2294, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 465, "dotnet.app.stack.walk.depth": 2, "dotnet.app.stubs": 1476, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 18, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 4.96, "dotnet.app.heap.bytes": 3779312, "dotnet.app.jit.bytes": 3145, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 71, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 293, "dotnet.app.loader.heap.bytes": 1519616, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 3, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 2, "dotnet.app.pinned.objects": 8, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 1409, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 8, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 193, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "inetmgr", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 20, "dotnet.app.ccws": 267, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 0, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 3682080, "dotnet.app.jit.bytes": 124776, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 2095, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 20, "dotnet.app.loaded.classes": 666, "dotnet.app.loader.heap.bytes": 3145728, "dotnet.app.loading.failures": 0, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 5, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 4, "dotnet.app.pinned.objects": 3, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0.1, "dotnet.app.runtime.checks": 885, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 457, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 1283, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}]}, "timeout": 60, "username": "Administrator"}, "************": {"errors": [], "object.ip": "************", "password": "Mind@123", "port": 5985, "result": {"dotnet.app": [{"dotnet.app": "_global_", "dotnet.app.appdomains": 2, "dotnet.app.assemblies": 38, "dotnet.app.ccws": 271, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 3, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 18, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 2.48, "dotnet.app.heap.bytes": 7461392, "dotnet.app.jit.bytes": 127921, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 2166, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 38, "dotnet.app.loaded.classes": 959, "dotnet.app.loader.heap.bytes": 4665344, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 8, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 6, "dotnet.app.pinned.objects": 11, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0.1, "dotnet.app.runtime.checks": 2294, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 465, "dotnet.app.stack.walk.depth": 2, "dotnet.app.stubs": 1476, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 18, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 4.96, "dotnet.app.heap.bytes": 3779312, "dotnet.app.jit.bytes": 3145, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 71, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 293, "dotnet.app.loader.heap.bytes": 1519616, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 3, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 2, "dotnet.app.pinned.objects": 8, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 1409, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 8, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 193, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "inetmgr", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 20, "dotnet.app.ccws": 267, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 0, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 3682080, "dotnet.app.jit.bytes": 124776, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 2095, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 20, "dotnet.app.loaded.classes": 666, "dotnet.app.loader.heap.bytes": 3145728, "dotnet.app.loading.failures": 0, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 5, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 4, "dotnet.app.pinned.objects": 3, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0.1, "dotnet.app.runtime.checks": 885, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 457, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 1283, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}]}, "timeout": 60, "username": "Administrator"}, "fd00:1:1:1::132": {"errors": [], "metric.timeout": 60, "object.ip": "fd00:1:1:1::132", "password": "Mind@123", "port": 5985, "result": {"dotnet.app": [{"dotnet.app": "powershell", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 5, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 8, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528040", "dotnet.app.framework.version": "4.8.03752", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 4336744, "dotnet.app.jit.bytes": 1919, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 34, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 350, "dotnet.app.loader.heap.bytes": 1794048, "dotnet.app.loading.failures": 0, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 16, "dotnet.app.peak.queue.length": 2, "dotnet.app.physical.threads": 14, "dotnet.app.pinned.objects": 2, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 24.07, "dotnet.app.runtime.checks": 2063, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 20, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 200, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "mmc", "dotnet.app.appdomains": 2, "dotnet.app.assemblies": 14, "dotnet.app.ccws": 50, "dotnet.app.channels": 2, "dotnet.app.contentions": 6, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 4, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 31, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528040", "dotnet.app.framework.version": "4.8.03752", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 3240464, "dotnet.app.jit.bytes": 2326, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 18, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 14, "dotnet.app.loaded.classes": 132, "dotnet.app.loader.heap.bytes": 647168, "dotnet.app.loading.failures": 2, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294967291, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 8, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 437628, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 31104, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 774, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 1227, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "yourphoneserver", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 61, "dotnet.app.ccws": 122, "dotnet.app.channels": 0, "dotnet.app.contentions": 16, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 1, "dotnet.app.exceptions": 18595, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528040", "dotnet.app.framework.version": "4.8.03752", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 3289304, "dotnet.app.jit.bytes": 256894, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 3815, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 61, "dotnet.app.loaded.classes": 2119, "dotnet.app.loader.heap.bytes": 7086080, "dotnet.app.loading.failures": 0, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 25, "dotnet.app.peak.queue.length": 2, "dotnet.app.physical.threads": 13, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 12, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 29596, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 290, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 289, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "sqlservr", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 2, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 0, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528040", "dotnet.app.framework.version": "4.8.03752", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 0, "dotnet.app.jit.bytes": 1988, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 45, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 2, "dotnet.app.loaded.classes": 23, "dotnet.app.loader.heap.bytes": 217088, "dotnet.app.loading.failures": 0, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 3, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 2, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 1, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 1, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 27, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "sqlceip", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 19, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 122, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 187332, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528040", "dotnet.app.framework.version": "4.8.03752", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 41009760, "dotnet.app.jit.bytes": 63932, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 452, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 19, "dotnet.app.loaded.classes": 354, "dotnet.app.loader.heap.bytes": 1081344, "dotnet.app.loading.failures": 2, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 16, "dotnet.app.peak.queue.length": 317, "dotnet.app.physical.threads": 4294967292, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 3, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 815021, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 27, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 379, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "_global_", "dotnet.app.appdomains": 7, "dotnet.app.assemblies": 135, "dotnet.app.ccws": 186, "dotnet.app.channels": 2, "dotnet.app.contentions": 144, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 13, "dotnet.app.current.queue.length": 1, "dotnet.app.exceptions": 206432, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528040", "dotnet.app.framework.version": "4.8.03752", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 59405760, "dotnet.app.jit.bytes": 405539, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 4925, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 135, "dotnet.app.loaded.classes": 3564, "dotnet.app.loader.heap.bytes": 13332480, "dotnet.app.loading.failures": 5, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 63, "dotnet.app.peak.queue.length": 338, "dotnet.app.physical.threads": 39, "dotnet.app.pinned.objects": 2, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 22, "dotnet.app.remote.calls": 437628, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 24.07, "dotnet.app.runtime.checks": 1063927, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 1152, "dotnet.app.stack.walk.depth": 6, "dotnet.app.stubs": 2526, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#1", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 21, "dotnet.app.ccws": 5, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 466, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528040", "dotnet.app.framework.version": "4.8.03752", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 7529488, "dotnet.app.jit.bytes": 78480, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 561, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 21, "dotnet.app.loaded.classes": 586, "dotnet.app.loader.heap.bytes": 2506752, "dotnet.app.loading.failures": 1, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 8, "dotnet.app.peak.queue.length": 17, "dotnet.app.physical.threads": 6, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 186142, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 40, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 404, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}]}, "username": "admin"}, "************": {"errors": [], "metric.timeout": 60, "object.ip": "************", "password": "Mind@123", "port": 5985, "result": {"dotnet.app": [{"dotnet.app": "mmc", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 9, "dotnet.app.ccws": 5, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 0, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461814", "dotnet.app.framework.version": "4.7.03190", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 0, "dotnet.app.jit.bytes": 0, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 0, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 9, "dotnet.app.loaded.classes": 18, "dotnet.app.loader.heap.bytes": 221184, "dotnet.app.loading.failures": 0, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 2, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 1, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 9.86, "dotnet.app.runtime.checks": 116, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 61, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 394, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "_global_", "dotnet.app.appdomains": 3, "dotnet.app.assemblies": 48, "dotnet.app.ccws": 15, "dotnet.app.channels": 0, "dotnet.app.contentions": 83, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 4, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 33, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461814", "dotnet.app.framework.version": "4.7.03190", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 10801992, "dotnet.app.jit.bytes": 68368, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 539, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 48, "dotnet.app.loaded.classes": 948, "dotnet.app.loader.heap.bytes": 4444160, "dotnet.app.loading.failures": 1, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 19, "dotnet.app.peak.queue.length": 11, "dotnet.app.physical.threads": 15, "dotnet.app.pinned.objects": 2, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 5, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 9.86, "dotnet.app.runtime.checks": 421890, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 113, "dotnet.app.stack.walk.depth": 3, "dotnet.app.stubs": 953, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#1", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 21, "dotnet.app.ccws": 5, "dotnet.app.channels": 0, "dotnet.app.contentions": 83, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 25, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461814", "dotnet.app.framework.version": "4.7.03190", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 6670296, "dotnet.app.jit.bytes": 66449, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 505, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 21, "dotnet.app.loaded.classes": 579, "dotnet.app.loader.heap.bytes": 2449408, "dotnet.app.loading.failures": 1, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 7, "dotnet.app.peak.queue.length": 10, "dotnet.app.physical.threads": 6, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 420315, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 36, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 367, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 5, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 8, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461814", "dotnet.app.framework.version": "4.7.03190", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 4131696, "dotnet.app.jit.bytes": 1919, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 34, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 351, "dotnet.app.loader.heap.bytes": 1773568, "dotnet.app.loading.failures": 0, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 10, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 8, "dotnet.app.pinned.objects": 2, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 1459, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 16, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 192, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}]}, "username": "Administrator"}, "************": {"errors": [], "object.ip": "************", "password": "Mind@123", "port": 5985, "result": {"dotnet.app": [{"dotnet.app": "w3wp", "dotnet.app.appdomains": 2, "dotnet.app.assemblies": 67, "dotnet.app.ccws": 56064, "dotnet.app.channels": 2, "dotnet.app.contentions": 105172, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 4, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 2183, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 11668816, "dotnet.app.jit.bytes": 90196, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 789, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 67, "dotnet.app.loaded.classes": 6181, "dotnet.app.loader.heap.bytes": 10330112, "dotnet.app.loading.failures": 0, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 94, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 27, "dotnet.app.pinned.objects": 3, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 76, "dotnet.app.remote.calls": 2, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 278410, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 112209, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 612, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#22", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 7, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 5974360, "dotnet.app.jit.bytes": 16280, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 200, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 8, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 7, "dotnet.app.pinned.objects": 3, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 21.36, "dotnet.app.runtime.checks": 333227, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 1977, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#14", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 5265952, "dotnet.app.jit.bytes": 14570, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 194, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 7, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 6, "dotnet.app.pinned.objects": 8, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 23.08, "dotnet.app.runtime.checks": 200805, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 920, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#5", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 6531096, "dotnet.app.jit.bytes": 12860, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 188, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 6, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 5, "dotnet.app.pinned.objects": 1, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 68388, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 581, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app.channels": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.jit.bytes": 0, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 0, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0}, {"dotnet.app": "powershell#21", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 3, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 5981856, "dotnet.app.jit.bytes": 16280, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 200, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 8, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 7, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 18.38, "dotnet.app.runtime.checks": 333224, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 1971, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#13", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 1, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 5283736, "dotnet.app.jit.bytes": 14570, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 194, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 7, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 6, "dotnet.app.pinned.objects": 8, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 27.26, "dotnet.app.runtime.checks": 200808, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 915, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#9", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 2, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 6523240, "dotnet.app.jit.bytes": 13430, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 190, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 7, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 6, "dotnet.app.pinned.objects": 2, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 112503, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 865, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#3", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 4385440, "dotnet.app.jit.bytes": 12262, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 182, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1589248, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 7, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 6, "dotnet.app.pinned.objects": 1, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 20.07, "dotnet.app.runtime.checks": 24238, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 369, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 238, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#2", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 17, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0.04, "dotnet.app.heap.bytes": 5788032, "dotnet.app.jit.bytes": 3341, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 77, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 292, "dotnet.app.loader.heap.bytes": 1482752, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 3, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 5657, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 8, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 191, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app.channels": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.jit.bytes": 17420, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 204, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0}, {"dotnet.app": "powershell#20", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 2, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 5628528, "dotnet.app.jit.bytes": 15710, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 198, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 8, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 7, "dotnet.app.pinned.objects": 1, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 23.31, "dotnet.app.runtime.checks": 289085, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 1785, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#15", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 5, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 5626400, "dotnet.app.jit.bytes": 15140, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 196, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 6, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 5, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 244944, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 1120, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#7", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 3, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 6532232, "dotnet.app.jit.bytes": 13430, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 190, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 7, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 6, "dotnet.app.pinned.objects": 1, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 112501, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 866, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#24", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 3, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 5991496, "dotnet.app.jit.bytes": 16850, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 202, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 7, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 6, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 25.31, "dotnet.app.runtime.checks": 377360, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 2168, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#17", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 5633600, "dotnet.app.jit.bytes": 15710, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 198, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 10, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 8, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 23.11, "dotnet.app.runtime.checks": 289083, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 1779, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#1", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 1, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 2.5, "dotnet.app.heap.bytes": 5901376, "dotnet.app.jit.bytes": 12262, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 182, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1589248, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 9, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 7, "dotnet.app.pinned.objects": 1, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 32.71, "dotnet.app.runtime.checks": 24238, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 357, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 238, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#23", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 5, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 5989888, "dotnet.app.jit.bytes": 16850, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 202, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 7, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 6, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 30.68, "dotnet.app.runtime.checks": 377363, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 2165, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#4", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 4382232, "dotnet.app.jit.bytes": 12262, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 182, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1589248, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 7, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 6, "dotnet.app.pinned.objects": 1, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 21.71, "dotnet.app.runtime.checks": 24238, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 369, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 238, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "c2wtshost", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 13, "dotnet.app.ccws": 0, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 0, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 0, "dotnet.app.jit.bytes": 800, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 23, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 13, "dotnet.app.loaded.classes": 161, "dotnet.app.loader.heap.bytes": 262144, "dotnet.app.loading.failures": 0, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 5, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 3, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0.18, "dotnet.app.runtime.checks": 80391, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 13, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 127, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#11", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 6525488, "dotnet.app.jit.bytes": 14000, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 192, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 9, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 8, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 25.41, "dotnet.app.runtime.checks": 156671, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 1528, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#6", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 2, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 5910352, "dotnet.app.jit.bytes": 12860, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 188, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 6, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 5, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 68389, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 584, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "_global_", "dotnet.app.appdomains": 30, "dotnet.app.assemblies": 566, "dotnet.app.ccws": 56172, "dotnet.app.channels": 2, "dotnet.app.contentions": 105221, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 57, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 2541, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0.1, "dotnet.app.heap.bytes": 171185368, "dotnet.app.jit.bytes": 466326, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 5864, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 566, "dotnet.app.loaded.classes": 17111, "dotnet.app.loader.heap.bytes": 53506048, "dotnet.app.loading.failures": 162, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 294, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 196, "dotnet.app.pinned.objects": 38, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 107, "dotnet.app.remote.calls": 2, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 5677459, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 145546, "dotnet.app.stack.walk.depth": 29, "dotnet.app.stubs": 7174, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#16", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 5635032, "dotnet.app.jit.bytes": 15140, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 196, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 6, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 5, "dotnet.app.pinned.objects": 1, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 244947, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 1125, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#12", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 6529000, "dotnet.app.jit.bytes": 14000, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 192, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 9, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 8, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 25.45, "dotnet.app.runtime.checks": 156668, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 1529, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 16, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0.3, "dotnet.app.heap.bytes": 7213616, "dotnet.app.jit.bytes": 4403, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 125, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 302, "dotnet.app.loader.heap.bytes": 1519616, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 5, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 4, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 28126, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 37, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 206, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#19", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 3, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 5630008, "dotnet.app.jit.bytes": 15710, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 198, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 8, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 7, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 26.28, "dotnet.app.runtime.checks": 289079, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 1781, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#18", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 3, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 5617160, "dotnet.app.jit.bytes": 15710, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 198, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 7, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 6, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 24.76, "dotnet.app.runtime.checks": 289086, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 2060, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#8", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 5, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 6521752, "dotnet.app.jit.bytes": 13430, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 190, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 7, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 6, "dotnet.app.pinned.objects": 4, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 112532, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 870, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "powershell#10", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 4, "dotnet.app.channels": 0, "dotnet.app.contentions": 2, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 13, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 6518536, "dotnet.app.jit.bytes": 14000, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 192, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 407, "dotnet.app.loader.heap.bytes": 1597440, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 9, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 8, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 26.81, "dotnet.app.runtime.checks": 156669, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 1529, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 242, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app.channels": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 0, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0}, {"dotnet.app.channels": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.framework.release": "528049", "dotnet.app.framework.version": "4.8.03761", "dotnet.app.jit.bytes": 16850, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 202, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0}]}, "timeout": 60, "username": "Administrator"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "password": "Mind@123", "port": 5985, "result": {"dotnet.app": [{"dotnet.app": "w3wp#12", "dotnet.app.appdomains": 2, "dotnet.app.assemblies": 84, "dotnet.app.ccws": 8, "dotnet.app.channels": 2, "dotnet.app.contentions": 704, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 4, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 23000, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 43752248, "dotnet.app.jit.bytes": 133599, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 754, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 84, "dotnet.app.loaded.classes": 3199, "dotnet.app.loader.heap.bytes": 4018176, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294965254, "dotnet.app.peak.queue.length": 5506, "dotnet.app.physical.threads": 48, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 13, "dotnet.app.remote.calls": 140813, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 3346863, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 75, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 827, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "w3wp#7", "dotnet.app.appdomains": 3, "dotnet.app.assemblies": 177, "dotnet.app.ccws": 12, "dotnet.app.channels": 3, "dotnet.app.contentions": 1136, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 6, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 199683, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 73073176, "dotnet.app.jit.bytes": 1235960, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 8661, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 177, "dotnet.app.loaded.classes": 18306, "dotnet.app.loader.heap.bytes": 32010240, "dotnet.app.loading.failures": 7, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294966521, "dotnet.app.peak.queue.length": 5502, "dotnet.app.physical.threads": 62, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 11, "dotnet.app.remote.calls": 1159811, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 940855, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 125, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 1212, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "noderunner#3", "dotnet.app.appdomains": 3, "dotnet.app.assemblies": 366, "dotnet.app.ccws": 3, "dotnet.app.channels": 5, "dotnet.app.contentions": 200719, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 8, "dotnet.app.current.queue.length": 26, "dotnet.app.exceptions": 117514, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 373639288, "dotnet.app.jit.bytes": 1959175, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 22300, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 196, "dotnet.app.loaded.classes": 24679, "dotnet.app.loader.heap.bytes": 37388288, "dotnet.app.loading.failures": 237, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294966549, "dotnet.app.peak.queue.length": 3465949031, "dotnet.app.physical.threads": 83, "dotnet.app.pinned.objects": 3, "dotnet.app.queue.length.per.sec": 360.16, "dotnet.app.recognized.threads": 3, "dotnet.app.remote.calls": 31069420, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 18150827, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 278, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 781, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 2}, {"dotnet.app": "microsoft.exchange.edgesyncsvc", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 44, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 12, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 382592, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0.07, "dotnet.app.heap.bytes": 12245904, "dotnet.app.jit.bytes": 67073, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 219, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 44, "dotnet.app.loaded.classes": 1412, "dotnet.app.loader.heap.bytes": 1916928, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294967290, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 7, "dotnet.app.pinned.objects": 141, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 238656, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 785451, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 26, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 414, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "msexchangemailboxassistants", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 191, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 50197993, "dotnet.app.contentions.per.sec": 2.99, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 51181644, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 562175224, "dotnet.app.jit.bytes": 423567, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 3963, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 191, "dotnet.app.loaded.classes": 10273, "dotnet.app.loader.heap.bytes": 11599872, "dotnet.app.loading.failures": 705, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294965507, "dotnet.app.peak.queue.length": 37, "dotnet.app.physical.threads": 153, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 4, "dotnet.app.remote.calls": 755958, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 3796019115, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 165, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 1871, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "msexchangedelivery", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 149, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 3177, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 1, "dotnet.app.exceptions": 2963, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 87293944, "dotnet.app.jit.bytes": 131119, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 761, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 149, "dotnet.app.loaded.classes": 7000, "dotnet.app.loader.heap.bytes": 6000640, "dotnet.app.loading.failures": 11, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294965585, "dotnet.app.peak.queue.length": 4, "dotnet.app.physical.threads": 28, "dotnet.app.pinned.objects": 1, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 3, "dotnet.app.remote.calls": 263105, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 41003380, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 62, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 1375, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "noderunner#1", "dotnet.app.appdomains": 3, "dotnet.app.assemblies": 482, "dotnet.app.ccws": 3, "dotnet.app.channels": 5, "dotnet.app.contentions": 1430, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 8, "dotnet.app.current.queue.length": 40, "dotnet.app.exceptions": 958589, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 151054560, "dotnet.app.jit.bytes": 2227540, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 25157, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 295, "dotnet.app.loaded.classes": 27114, "dotnet.app.loader.heap.bytes": 43044864, "dotnet.app.loading.failures": 299, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294967114, "dotnet.app.peak.queue.length": 800038895, "dotnet.app.physical.threads": 111, "dotnet.app.pinned.objects": 5, "dotnet.app.queue.length.per.sec": 82.81, "dotnet.app.recognized.threads": 3, "dotnet.app.remote.calls": 5155013, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 5086917, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 444, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 811, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 2}, {"dotnet.app": "powershell", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 19, "dotnet.app.ccws": 5, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 8, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 4.14, "dotnet.app.heap.bytes": 8727240, "dotnet.app.jit.bytes": 3217, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 29, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 19, "dotnet.app.loaded.classes": 359, "dotnet.app.loader.heap.bytes": 1789952, "dotnet.app.loading.failures": 0, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 14, "dotnet.app.peak.queue.length": 2, "dotnet.app.physical.threads": 12, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 10358, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 18, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 198, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "w3wp#4", "dotnet.app.appdomains": 2, "dotnet.app.assemblies": 117, "dotnet.app.ccws": 4, "dotnet.app.channels": 2, "dotnet.app.contentions": 1499, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 4, "dotnet.app.current.queue.length": 1, "dotnet.app.exceptions": 10008001, "dotnet.app.exceptions.per.sec": 1, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 39037376, "dotnet.app.jit.bytes": 94152, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 538, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 117, "dotnet.app.loaded.classes": 5401, "dotnet.app.loader.heap.bytes": 4792320, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294965899, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 53, "dotnet.app.pinned.objects": 130, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 8, "dotnet.app.remote.calls": 213321, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 2492, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 60, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 826, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "edgetransport", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 146, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 7178, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 6, "dotnet.app.exceptions": 25617, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0.01, "dotnet.app.heap.bytes": 49178896, "dotnet.app.jit.bytes": 205303, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 1120, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 146, "dotnet.app.loaded.classes": 3917, "dotnet.app.loader.heap.bytes": 5894144, "dotnet.app.loading.failures": 7, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294964442, "dotnet.app.peak.queue.length": 1983488, "dotnet.app.physical.threads": 89, "dotnet.app.pinned.objects": 141, "dotnet.app.queue.length.per.sec": 1, "dotnet.app.recognized.threads": 9, "dotnet.app.remote.calls": 794077, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 1223665, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 72, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 1394, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "msexchangefrontendtransport", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 84, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 7553, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 26474, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 38706608, "dotnet.app.jit.bytes": 89620, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 379, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 84, "dotnet.app.loaded.classes": 2387, "dotnet.app.loader.heap.bytes": 3235840, "dotnet.app.loading.failures": 4, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294966277, "dotnet.app.peak.queue.length": 2, "dotnet.app.physical.threads": 54, "dotnet.app.pinned.objects": 13, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 3, "dotnet.app.remote.calls": 127116, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 2674657, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 52, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 769, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "umservice", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 66, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 698, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 15110, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 22091024, "dotnet.app.jit.bytes": 71587, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 279, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 66, "dotnet.app.loaded.classes": 1826, "dotnet.app.loader.heap.bytes": 2338816, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294966974, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 67, "dotnet.app.pinned.objects": 3, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 3, "dotnet.app.remote.calls": 95628, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 400766, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 27, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 506, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "wmsvc", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 5, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 0, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 118104, "dotnet.app.jit.bytes": 4007, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 27, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 5, "dotnet.app.loaded.classes": 18, "dotnet.app.loader.heap.bytes": 266240, "dotnet.app.loading.failures": 0, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 2, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 1, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 3, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 1, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 26, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "msexchangemailboxreplication", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 95, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 8267, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 267956, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0.01, "dotnet.app.heap.bytes": 46849352, "dotnet.app.jit.bytes": 140159, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 641, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 95, "dotnet.app.loaded.classes": 5353, "dotnet.app.loader.heap.bytes": 3989504, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294964273, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 71, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 6, "dotnet.app.remote.calls": 1038350, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 51407794, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 64, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 778, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "msexchangedagmgmt", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 60, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 15, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 33120, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 18607288, "dotnet.app.jit.bytes": 66521, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 246, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 60, "dotnet.app.loaded.classes": 1595, "dotnet.app.loader.heap.bytes": 2240512, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294966456, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 15, "dotnet.app.pinned.objects": 129, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 388832, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 730862, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 42, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 540, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "microsoft.activedirectory.webservices", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 26, "dotnet.app.ccws": 0, "dotnet.app.channels": 0, "dotnet.app.contentions": 88, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 632540, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 11.87, "dotnet.app.heap.bytes": 36436128, "dotnet.app.jit.bytes": 102131, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 496, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 26, "dotnet.app.loaded.classes": 599, "dotnet.app.loader.heap.bytes": 1286144, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294967259, "dotnet.app.peak.queue.length": 9, "dotnet.app.physical.threads": 12, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 424428, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 31, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 236, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "w3wp#8", "dotnet.app.appdomains": 2, "dotnet.app.assemblies": 73, "dotnet.app.ccws": 4, "dotnet.app.channels": 2, "dotnet.app.contentions": 925, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 4, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 88985, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 20657880, "dotnet.app.jit.bytes": 94586, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 513, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 73, "dotnet.app.loaded.classes": 2356, "dotnet.app.loader.heap.bytes": 2793472, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294966103, "dotnet.app.peak.queue.length": 43, "dotnet.app.physical.threads": 29, "dotnet.app.pinned.objects": 129, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 7, "dotnet.app.remote.calls": 848972, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 1143484, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 52, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 632, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "w3wp#2", "dotnet.app.appdomains": 3, "dotnet.app.assemblies": 177, "dotnet.app.ccws": 19, "dotnet.app.channels": 3, "dotnet.app.contentions": 869, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 6, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 71470, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0.01, "dotnet.app.heap.bytes": 150447360, "dotnet.app.jit.bytes": 1904697, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 12729, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 177, "dotnet.app.loaded.classes": 18898, "dotnet.app.loader.heap.bytes": 37969920, "dotnet.app.loading.failures": 7, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294967060, "dotnet.app.peak.queue.length": 808, "dotnet.app.physical.threads": 84, "dotnet.app.pinned.objects": 300, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 7, "dotnet.app.remote.calls": 365769, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 1187745, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 372, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 1423, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "msexchangetransportlogsearch", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 51, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 125, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 18443, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 16793128, "dotnet.app.jit.bytes": 63935, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 232, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 51, "dotnet.app.loaded.classes": 1407, "dotnet.app.loader.heap.bytes": 2072576, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 10, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 8, "dotnet.app.pinned.objects": 130, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 4984, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 334142, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 26, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 421, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "microsoft.exchange.antispamupdatesvc", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 17, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 0, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 1269880, "dotnet.app.jit.bytes": 2853, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 21, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 17, "dotnet.app.loaded.classes": 51, "dotnet.app.loader.heap.bytes": 413696, "dotnet.app.loading.failures": 0, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 44, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 42, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 372475, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 23, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 106, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "noderunner#2", "dotnet.app.appdomains": 3, "dotnet.app.assemblies": 495, "dotnet.app.ccws": 3, "dotnet.app.channels": 5, "dotnet.app.contentions": 8536, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 8, "dotnet.app.current.queue.length": 33, "dotnet.app.exceptions": 95730, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 295014720, "dotnet.app.jit.bytes": 1088583533, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 37804583, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 320, "dotnet.app.loaded.classes": 29229, "dotnet.app.loader.heap.bytes": 61952000, "dotnet.app.loading.failures": 186, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294967198, "dotnet.app.peak.queue.length": 1196888043, "dotnet.app.physical.threads": 105, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 92.78, "dotnet.app.recognized.threads": 3, "dotnet.app.remote.calls": 2984523, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 3.48, "dotnet.app.runtime.checks": 194707094, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 2126, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 1060, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 2}, {"dotnet.app": "w3wp#14", "dotnet.app.appdomains": 3, "dotnet.app.assemblies": 245, "dotnet.app.ccws": 12, "dotnet.app.channels": 3, "dotnet.app.contentions": 5370, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 6, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 220780, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 161876376, "dotnet.app.jit.bytes": 1688024, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 10610, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 245, "dotnet.app.loaded.classes": 18369, "dotnet.app.loader.heap.bytes": 32604160, "dotnet.app.loading.failures": 11, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294965823, "dotnet.app.peak.queue.length": 5499, "dotnet.app.physical.threads": 129, "dotnet.app.pinned.objects": 131, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 11, "dotnet.app.remote.calls": 657888, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 2849685, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 164, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 1574, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "microsoft.exchange.um.callrouter", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 66, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 412, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 15235, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 26420216, "dotnet.app.jit.bytes": 736365, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 3628, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 66, "dotnet.app.loaded.classes": 4895, "dotnet.app.loader.heap.bytes": 15740928, "dotnet.app.loading.failures": 33, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294967196, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 29, "dotnet.app.pinned.objects": 3, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 3, "dotnet.app.remote.calls": 95604, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 334789, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 30, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 529, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "w3wp#13", "dotnet.app.appdomains": 2, "dotnet.app.assemblies": 94, "dotnet.app.ccws": 24, "dotnet.app.channels": 2, "dotnet.app.contentions": 555, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 4, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 11050, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 50443608, "dotnet.app.jit.bytes": 120100, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 611, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 94, "dotnet.app.loaded.classes": 3268, "dotnet.app.loader.heap.bytes": 3891200, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294965790, "dotnet.app.peak.queue.length": 5498, "dotnet.app.physical.threads": 46, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 6, "dotnet.app.remote.calls": 671923, "dotnet.app.remote.calls.per.sec": 3.99, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 682, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 75, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 729, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "microsoft.exchange.search.service", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 89, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 68, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 2, "dotnet.app.exceptions": 45944, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 40303680, "dotnet.app.jit.bytes": 137300, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 466, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 89, "dotnet.app.loaded.classes": 2224, "dotnet.app.loader.heap.bytes": 2703360, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 0, "dotnet.app.peak.queue.length": 297139, "dotnet.app.physical.threads": 25, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 3, "dotnet.app.remote.calls": 714844, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 146566, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 76, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 643, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "hostcontrollerservice", "dotnet.app.appdomains": 2, "dotnet.app.assemblies": 137, "dotnet.app.ccws": 0, "dotnet.app.channels": 3, "dotnet.app.contentions": 226, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 5, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 115, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 7612680, "dotnet.app.jit.bytes": 1085370, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 7414, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 136, "dotnet.app.loaded.classes": 2548, "dotnet.app.loader.heap.bytes": 10231808, "dotnet.app.loading.failures": 60, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294967288, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 32, "dotnet.app.pinned.objects": 6, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 3, "dotnet.app.remote.calls": 10256700, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 46606573, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 19, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 582, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 1}, {"dotnet.app": "w3wp#3", "dotnet.app.appdomains": 2, "dotnet.app.assemblies": 7, "dotnet.app.ccws": 23, "dotnet.app.channels": 2, "dotnet.app.contentions": 873, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 4, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 6, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 1056416, "dotnet.app.jit.bytes": 211, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 2, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 7, "dotnet.app.loaded.classes": 35, "dotnet.app.loader.heap.bytes": 225280, "dotnet.app.loading.failures": 0, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294966020, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 31, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 8, "dotnet.app.remote.calls": 2, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 152776, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 43, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 154, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "microsoft.exchange.diagnostics.service", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 46, "dotnet.app.ccws": 3, "dotnet.app.channels": 0, "dotnet.app.contentions": 5, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 7874, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 7.92, "dotnet.app.heap.bytes": 60264536, "dotnet.app.jit.bytes": 182990, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 555, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 46, "dotnet.app.loaded.classes": 904, "dotnet.app.loader.heap.bytes": 1839104, "dotnet.app.loading.failures": 2, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294966679, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 18, "dotnet.app.pinned.objects": 19, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 60942223, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 47, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 425, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "w3wp", "dotnet.app.appdomains": 3, "dotnet.app.assemblies": 241, "dotnet.app.ccws": 12, "dotnet.app.channels": 3, "dotnet.app.contentions": 101, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 6, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 27163, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 98468952, "dotnet.app.jit.bytes": 1872852, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 11931, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 241, "dotnet.app.loaded.classes": 24233, "dotnet.app.loader.heap.bytes": 42442752, "dotnet.app.loading.failures": 9, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 18, "dotnet.app.peak.queue.length": 135, "dotnet.app.physical.threads": 71, "dotnet.app.pinned.objects": 1, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 8, "dotnet.app.remote.calls": 59918, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0.35, "dotnet.app.runtime.checks": 463206, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 142, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 1490, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "microsoft.exchange.store.worker", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 108, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 269, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 69032, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 44577704, "dotnet.app.jit.bytes": 1987261, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 9644, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 108, "dotnet.app.loaded.classes": 4716, "dotnet.app.loader.heap.bytes": 17625088, "dotnet.app.loading.failures": 5, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 23, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 16, "dotnet.app.pinned.objects": 1, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 11, "dotnet.app.remote.calls": 155198, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 166369655, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 76, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 868, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "microsoft.exchange.servicehost", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 127, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 37754, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 18778, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 35236128, "dotnet.app.jit.bytes": 140808, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 747, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 127, "dotnet.app.loaded.classes": 5352, "dotnet.app.loader.heap.bytes": 4657152, "dotnet.app.loading.failures": 6, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294964382, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 64, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 5, "dotnet.app.remote.calls": 846078, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 7595350, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 70, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 1079, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "w3wp#9", "dotnet.app.appdomains": 2, "dotnet.app.assemblies": 107, "dotnet.app.ccws": 12, "dotnet.app.channels": 2, "dotnet.app.contentions": 826, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 4, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 89725, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 41538704, "dotnet.app.jit.bytes": 136503, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 971, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 107, "dotnet.app.loaded.classes": 5770, "dotnet.app.loader.heap.bytes": 4108288, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294966058, "dotnet.app.peak.queue.length": 478, "dotnet.app.physical.threads": 51, "dotnet.app.pinned.objects": 129, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 9, "dotnet.app.remote.calls": 956274, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 115486418, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 83, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 857, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "w3wp#6", "dotnet.app.appdomains": 2, "dotnet.app.assemblies": 298, "dotnet.app.ccws": 6, "dotnet.app.channels": 2, "dotnet.app.contentions": 57877, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 4, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 634161, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0.07, "dotnet.app.heap.bytes": 355547080, "dotnet.app.jit.bytes": 70081162, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 281430, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 298, "dotnet.app.loaded.classes": 24920, "dotnet.app.loader.heap.bytes": 73666560, "dotnet.app.loading.failures": 14, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294963950, "dotnet.app.peak.queue.length": 110936, "dotnet.app.physical.threads": 104, "dotnet.app.pinned.objects": 2, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 15, "dotnet.app.remote.calls": 1525821, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 44854629, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 256, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 1547, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "w3wp#1", "dotnet.app.appdomains": 3, "dotnet.app.assemblies": 326, "dotnet.app.ccws": 7, "dotnet.app.channels": 3, "dotnet.app.contentions": 146, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 6, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 14499, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0.01, "dotnet.app.heap.bytes": 131614872, "dotnet.app.jit.bytes": 2638292, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 18709, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 326, "dotnet.app.loaded.classes": 24807, "dotnet.app.loader.heap.bytes": 78655488, "dotnet.app.loading.failures": 11, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294967284, "dotnet.app.peak.queue.length": 803, "dotnet.app.physical.threads": 49, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 9, "dotnet.app.remote.calls": 62679, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 439066, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 128, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 1391, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "_global_", "dotnet.app.appdomains": 81, "dotnet.app.assemblies": 6712, "dotnet.app.ccws": 273, "dotnet.app.channels": 58, "dotnet.app.contentions": 50624325, "dotnet.app.contentions.per.sec": 2.99, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 151, "dotnet.app.current.queue.length": 130, "dotnet.app.exceptions": 69683078, "dotnet.app.exceptions.per.sec": 3.99, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0.47, "dotnet.app.heap.bytes": 4064988336, "dotnet.app.jit.bytes": 1183142467, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 38266574, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 5995, "dotnet.app.loaded.classes": 380761, "dotnet.app.loader.heap.bytes": 667164672, "dotnet.app.loading.failures": 2437, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294910749, "dotnet.app.peak.queue.length": 1862448371, "dotnet.app.physical.threads": 2637, "dotnet.app.pinned.objects": 1583, "dotnet.app.queue.length.per.sec": 611.57, "dotnet.app.recognized.threads": 278, "dotnet.app.remote.calls": 69517056, "dotnet.app.remote.calls.per.sec": 3.99, "dotnet.app.rt.check.time.percent": 3.84, "dotnet.app.runtime.checks": 1102150222, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 6409, "dotnet.app.stack.walk.depth": 52, "dotnet.app.stubs": 41636, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 9}, {"dotnet.app": "w3wp#5", "dotnet.app.appdomains": 2, "dotnet.app.assemblies": 88, "dotnet.app.ccws": 4, "dotnet.app.channels": 2, "dotnet.app.contentions": 1967, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 4, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 129881, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 39104120, "dotnet.app.jit.bytes": 146273, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 730, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 88, "dotnet.app.loaded.classes": 2508, "dotnet.app.loader.heap.bytes": 3612672, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294965884, "dotnet.app.peak.queue.length": 2, "dotnet.app.physical.threads": 69, "dotnet.app.pinned.objects": 1, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 7, "dotnet.app.remote.calls": 117899, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 8246730, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 83, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 777, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "msexchangehmrecovery", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 18, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 0, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 1212720, "dotnet.app.jit.bytes": 2475, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 19, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 18, "dotnet.app.loaded.classes": 61, "dotnet.app.loader.heap.bytes": 311296, "dotnet.app.loading.failures": 0, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 36, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 35, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 333896, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 19, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 123, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "msexchangetransport", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 54, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 14355, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 18735200, "dotnet.app.jit.bytes": 64619, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 234, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 54, "dotnet.app.loaded.classes": 1740, "dotnet.app.loader.heap.bytes": 2043904, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 26, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 23, "dotnet.app.pinned.objects": 131, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 3, "dotnet.app.remote.calls": 95424, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 334227, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 25, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 474, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "microsoft.exchange.rpcclientaccess.service", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 96, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 60, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 1191, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 27963976, "dotnet.app.jit.bytes": 86925, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 372, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 96, "dotnet.app.loaded.classes": 5325, "dotnet.app.loader.heap.bytes": 3182592, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 37, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 34, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 8, "dotnet.app.remote.calls": 2152233, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 114882431, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 56, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 684, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "w3wp#11", "dotnet.app.appdomains": 2, "dotnet.app.assemblies": 73, "dotnet.app.ccws": 12, "dotnet.app.channels": 2, "dotnet.app.contentions": 839, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 4, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 1454514, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 62941120, "dotnet.app.jit.bytes": 141251, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 822, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 73, "dotnet.app.loaded.classes": 2705, "dotnet.app.loader.heap.bytes": 3612672, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294965588, "dotnet.app.peak.queue.length": 5501, "dotnet.app.physical.threads": 49, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 16, "dotnet.app.remote.calls": 140064, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 7160776, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 92, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 796, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "msexchangehmhost", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 114, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 102, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 90, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0.03, "dotnet.app.heap.bytes": 47541568, "dotnet.app.jit.bytes": 131132, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 559, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 114, "dotnet.app.loaded.classes": 8803, "dotnet.app.loader.heap.bytes": 3440640, "dotnet.app.loading.failures": 7, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294965472, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 16, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 4, "dotnet.app.remote.calls": 360049, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 1907591, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 32, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 755, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "msexchangecompliance", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 74, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 34673, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 14352, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 18888960, "dotnet.app.jit.bytes": 74783, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 286, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 74, "dotnet.app.loaded.classes": 2190, "dotnet.app.loader.heap.bytes": 2560000, "dotnet.app.loading.failures": 27, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294956371, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 136, "dotnet.app.pinned.objects": 3, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 260625, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 334221, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 27, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 551, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "microsoft.exchange.store.service", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 80, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 1, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 12974, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 32559472, "dotnet.app.jit.bytes": 310075, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 2937, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 80, "dotnet.app.loaded.classes": 2478, "dotnet.app.loader.heap.bytes": 6197248, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 20, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 11, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 9, "dotnet.app.remote.calls": 108690, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 6329730, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 37, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 614, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "noderunner", "dotnet.app.appdomains": 3, "dotnet.app.assemblies": 380, "dotnet.app.ccws": 3, "dotnet.app.channels": 5, "dotnet.app.contentions": 118, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 8, "dotnet.app.current.queue.length": 19, "dotnet.app.exceptions": 279, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 176816592, "dotnet.app.jit.bytes": 1634581, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 19031, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 196, "dotnet.app.loaded.classes": 26311, "dotnet.app.loader.heap.bytes": 35868672, "dotnet.app.loading.failures": 267, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294965489, "dotnet.app.peak.queue.length": 691266314, "dotnet.app.physical.threads": 36, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 71.83, "dotnet.app.recognized.threads": 3, "dotnet.app.remote.calls": 2888992, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 1870605, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 70, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 656, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 2}, {"dotnet.app": "forefrontactivedirectoryconnector", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 61, "dotnet.app.ccws": 14, "dotnet.app.channels": 0, "dotnet.app.contentions": 100, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 1600, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 15426976, "dotnet.app.jit.bytes": 163401, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 1050, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 61, "dotnet.app.loaded.classes": 1982, "dotnet.app.loader.heap.bytes": 6262784, "dotnet.app.loading.failures": 5, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 11, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 10, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 1, "dotnet.app.remote.calls": 4024, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 1085, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 41, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 414, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "microsoft.exchange.directory.topologyservice", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 75, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 80, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 1, "dotnet.app.exceptions": 1064873, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 18510144, "dotnet.app.jit.bytes": 66447, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 259, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 75, "dotnet.app.loaded.classes": 4498, "dotnet.app.loader.heap.bytes": 2318336, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294966844, "dotnet.app.peak.queue.length": 209418, "dotnet.app.physical.threads": 41, "dotnet.app.pinned.objects": 8, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 3, "dotnet.app.remote.calls": 0, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 4884833, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 39, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 561, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "umworkerprocess", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 118, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 230, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 1718, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 44325096, "dotnet.app.jit.bytes": 74252, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 285, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 118, "dotnet.app.loaded.classes": 1862, "dotnet.app.loader.heap.bytes": 3182592, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 68, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 70, "dotnet.app.pinned.objects": 4, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 3, "dotnet.app.remote.calls": 10752, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 1764, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 25, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 481, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "msexchangehmworker", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 240, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 15100, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 553012, "dotnet.app.exceptions.per.sec": 2.99, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0.08, "dotnet.app.heap.bytes": 255653240, "dotnet.app.jit.bytes": 1172774, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 6275, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 240, "dotnet.app.loaded.classes": 14807, "dotnet.app.loader.heap.bytes": 21921792, "dotnet.app.loading.failures": 435, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294962734, "dotnet.app.peak.queue.length": 637042, "dotnet.app.physical.threads": 118, "dotnet.app.pinned.objects": 14, "dotnet.app.queue.length.per.sec": 2.99, "dotnet.app.recognized.threads": 7, "dotnet.app.remote.calls": 1097031, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 18804028, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 147, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 2968, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "msexchangerepl", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 89, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 1114, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 1065391, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 19343224, "dotnet.app.jit.bytes": 82751, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 308, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 89, "dotnet.app.loaded.classes": 2610, "dotnet.app.loader.heap.bytes": 2863104, "dotnet.app.loading.failures": 4, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294967183, "dotnet.app.peak.queue.length": 0, "dotnet.app.physical.threads": 45, "dotnet.app.pinned.objects": 5, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 4, "dotnet.app.remote.calls": 122076, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 19634374, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 102, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 763, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "w3wp#10", "dotnet.app.appdomains": 2, "dotnet.app.assemblies": 83, "dotnet.app.ccws": 24, "dotnet.app.channels": 2, "dotnet.app.contentions": 580, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 4, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 23021, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 44595992, "dotnet.app.jit.bytes": 116520, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 600, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 83, "dotnet.app.loaded.classes": 2316, "dotnet.app.loader.heap.bytes": 3276800, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294965987, "dotnet.app.peak.queue.length": 5502, "dotnet.app.physical.threads": 45, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 8, "dotnet.app.remote.calls": 140032, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 608, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 133, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 654, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "complianceauditservice", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 81, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 122, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 15954, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 47622448, "dotnet.app.jit.bytes": 285576, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 1651, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 81, "dotnet.app.loaded.classes": 5839, "dotnet.app.loader.heap.bytes": 4579328, "dotnet.app.loading.failures": 7, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 28, "dotnet.app.peak.queue.length": 8, "dotnet.app.physical.threads": 26, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 2, "dotnet.app.remote.calls": 96587, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 337212, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 47, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 734, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "msexchangesubmission", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 119, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 23833, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 2, "dotnet.app.current.queue.length": 1, "dotnet.app.exceptions": 11730, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 72201640, "dotnet.app.jit.bytes": 111954, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 548, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 119, "dotnet.app.loaded.classes": 5571, "dotnet.app.loader.heap.bytes": 4489216, "dotnet.app.loading.failures": 5, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294965751, "dotnet.app.peak.queue.length": 4, "dotnet.app.physical.threads": 59, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 3, "dotnet.app.remote.calls": 179837, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 645524261, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 60, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 1125, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}, {"dotnet.app": "msexchangethrottling", "dotnet.app.appdomains": 1, "dotnet.app.assemblies": 54, "dotnet.app.ccws": 2, "dotnet.app.channels": 0, "dotnet.app.contentions": 0, "dotnet.app.contentions.per.sec": 0, "dotnet.app.context.bound.objects": 0, "dotnet.app.context.bound.proxies": 0, "dotnet.app.context.proxies": 0, "dotnet.app.contexts": 1, "dotnet.app.current.queue.length": 0, "dotnet.app.exceptions": 14342, "dotnet.app.exceptions.per.sec": 0, "dotnet.app.framework.release": "461310", "dotnet.app.framework.version": "4.7.02558", "dotnet.app.gc.time.percent": 0, "dotnet.app.heap.bytes": 19415568, "dotnet.app.jit.bytes": 65106, "dotnet.app.jit.failures": 0, "dotnet.app.jit.methods": 242, "dotnet.app.link.time.checks": 0, "dotnet.app.loaded.assemblies": 54, "dotnet.app.loaded.classes": 1735, "dotnet.app.loader.heap.bytes": 2035712, "dotnet.app.loading.failures": 3, "dotnet.app.loading.time.percent": 0, "dotnet.app.logical.threads": 4294966830, "dotnet.app.peak.queue.length": 1, "dotnet.app.physical.threads": 15, "dotnet.app.pinned.objects": 0, "dotnet.app.queue.length.per.sec": 0, "dotnet.app.recognized.threads": 4, "dotnet.app.remote.calls": 95464, "dotnet.app.remote.calls.per.sec": 0, "dotnet.app.rt.check.time.percent": 0, "dotnet.app.runtime.checks": 334155, "dotnet.app.signature.authentication.percent": 0, "dotnet.app.sink.blocks": 24, "dotnet.app.stack.walk.depth": 1, "dotnet.app.stubs": 432, "dotnet.app.threads.per.sec": 0, "dotnet.app.unloaded.appdomains": 0}]}, "username": "Administrator"}}