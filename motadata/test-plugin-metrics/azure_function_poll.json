{"127.0.0.1": {"217": {"metadata.fields": {"name": "FunctionApp120210317184034", "type": "functionapp"}}, "_type": "1", "cloud.client.id": "aaa130f3-5ed7-404b-87f5-90a5a42750ea", "cloud.secret.key": "**********************************", "cloud.tenant.id": "5b4acec3-2592-4187-9489-98c654cc6c87", "cloud.type": "Azure Cloud", "credential.profile.name": "Azure-Cloud-Test1655203790900", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 24, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "Azure-Cloud-Test-1655203790903", "discovery.object.type": "Azure Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:20:25.670 pm 14/06/2022", "discovery.total.objects": 24, "errors": [], "event.id": 176993700500113, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Azure Function", "metric.object": ***************, "metric.plugin": "azurefunction", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Azure Function", "object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:35.712 pm 14/06/2022", "object.custom.fields": {"***************": "functionapp", "***************": "FunctionApp120210317184034"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 46, "object.name": "FunctionApp120210317184034(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.state": "ENABLE", "object.target": "FunctionApp120210317184034(motadata-freetier)", "object.type": "Azure Function", "object.vendor": "Azure Cloud", "plugin.engine": "go", "plugin.id": 217, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"217": {"metadata.fields": {"name": "FunctionApp120210317184034", "type": "functionapp"}}, "azure.function.5xx.requests": 0, "azure.function.app.domains": 0, "azure.function.availability.status": "Normal", "azure.function.connections": 0, "azure.function.current.assemblies": 0, "azure.function.execution.units": 0, "azure.function.executions": 0, "azure.function.gen.0.collections": 0, "azure.function.gen.1.collections": 0, "azure.function.gen.2.collections": 0, "azure.function.handles": 0, "azure.function.last.modified.time": "2022-05-08 07:29:11.9233333 +0000 UTC", "azure.function.memory.used.bytes": 0, "azure.function.other.bytes.per.sec": 0, "azure.function.other.ops.per.sec": 0, "azure.function.private.bytes": 0, "azure.function.read.bytes.per.sec": 0, "azure.function.read.ops.per.sec": 0, "azure.function.request.queued.requests": 0, "azure.function.sent.bytes": 0, "azure.function.site.name": "FunctionApp120210317184034", "azure.function.threads": 0, "azure.function.unloaded.app.domains": 0, "azure.function.write.bytes.per.sec": 0, "azure.function.write.ops.per.sec": 0, "azure.location": "South Central US", "azure.name": "FunctionApp120210317184034", "azure.status": "Running", "azure.type": "Microsoft.Web/sites"}, "status": "succeed", "timeout": 60}}