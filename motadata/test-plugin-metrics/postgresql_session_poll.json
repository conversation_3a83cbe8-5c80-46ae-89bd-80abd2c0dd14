{"172.16.8.165": {"result": {"correlation.metrics": ["postgres.session", "postgres.session.lock"], "postgres.session": [{"postgres.session.id": 64306, "postgres.session.username": "postgres", "postgres.session.state": "idle in transaction", "postgres.session.remote.client": "172.16.10.116", "postgres.session.start.time": "2022-06-08 06:53:01.681523+00:00", "postgres.session.query": "select * from pg_catalog.pg_user"}], "postgres.active.sessions": 0, "postgres.idle.sessions": 0, "postgres.session.lock": [{"postgres.session.lock.id": 64315, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64315, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64315, "postgres.session.lock.type": "virtualxid", "postgres.session.lock.mode": "ExclusiveLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64306, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64306, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64306, "postgres.session.lock.type": "virtualxid", "postgres.session.lock.mode": "ExclusiveLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64315, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64315, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64315, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64315, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64315, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64306, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64306, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64306, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64315, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64306, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64306, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}], "postgres.held.locks": 17, "postgres.wait.locks": 0}, "errors": []}, "fd00:1:1:1::132": {"result": {"correlation.metrics": ["postgres.session", "postgres.session.lock"], "postgres.session": [{"postgres.session.id": 64306, "postgres.session.username": "postgres", "postgres.session.state": "idle in transaction", "postgres.session.remote.client": "172.16.10.116", "postgres.session.start.time": "2022-06-08 06:53:01.681523+00:00", "postgres.session.query": "select * from pg_catalog.pg_user"}], "postgres.active.sessions": 0, "postgres.idle.sessions": 0, "postgres.session.lock": [{"postgres.session.lock.id": 64315, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64315, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64315, "postgres.session.lock.type": "virtualxid", "postgres.session.lock.mode": "ExclusiveLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64306, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64306, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64306, "postgres.session.lock.type": "virtualxid", "postgres.session.lock.mode": "ExclusiveLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64315, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64315, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64315, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64315, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64315, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64306, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64306, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64306, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64315, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64306, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}, {"postgres.session.lock.id": 64306, "postgres.session.lock.type": "relation", "postgres.session.lock.mode": "AccessShareLock", "postgres.session.lock.granted": "True"}], "postgres.held.locks": 17, "postgres.wait.locks": 0}, "errors": []}}