[{"_type": "0", "id": 10000000000249, "visualization.name": "Total Connection", "visualization.description": "AzureFunction Total Connection", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.function.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.function.connections", "icon": {"name": "sitemap", "placement": "prefix"}}, "header": {"title": "Connections", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.function.connections.last"}]}}}}, {"_type": "0", "id": 10000000000250, "visualization.name": "Function Execution Unit", "visualization.description": "AzureFunction Function Execution Unit", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.execution.units", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.function.execution.units"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.function.execution.units", "icon": {"name": "function", "placement": "prefix"}}, "header": {"title": "Execution Unit", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.function.execution.units.last"}]}}}}, {"_type": "0", "id": 10000000000251, "visualization.name": "HTTP 5XX", "visualization.description": "AzureFunction HTTP 5XX", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.5xx.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.function.5xx.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.function.5xx.requests", "icon": {"name": "sessions", "placement": "prefix"}}, "header": {"title": "HTTP 5XX", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.function.5xx.requests.last"}]}}}}, {"_type": "0", "id": 10000000000252, "visualization.name": "Throughput", "visualization.description": "AzureFunction Throughput", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.read.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.function.write.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.function.read.bytes.per.sec"}, {"type": "metric", "data.point": "azure.function.write.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.function.read.bytes.per.sec", "icon": {"name": "tacho-meter", "placement": "prefix"}}, "header": {"title": "Throughput", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "azure.function.read.bytes.per.sec.last"}, {"label": "Write", "value": "azure.function.write.bytes.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000253, "visualization.name": "IOPS", "visualization.description": "AzureFunction IOPS", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.read.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.function.write.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.function.read.ops.per.sec"}, {"type": "metric", "data.point": "azure.function.write.ops.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.function.write.ops.per.sec", "icon": {"name": "rtt", "placement": "prefix"}}, "header": {"title": "IOPS", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "azure.function.read.ops.per.sec.last"}, {"label": "Write", "value": "azure.function.write.ops.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000254, "visualization.name": "Queued Requests", "visualization.description": "AzureFunction Queued Requests", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.request.queued.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.function.request.queued.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.function.request.queued.requests", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Queued Requests", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.function.request.queued.requests.last"}]}}}}, {"_type": "0", "id": 10000000000255, "visualization.name": "Connections Details", "visualization.description": "AzureFunction Connections Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000256, "visualization.name": "Execution Units", "visualization.description": "AzureFunction Execution Units", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.execution.units", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.function.handles", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000257, "visualization.name": "Function Operation Details", "visualization.description": "AzureFunction Function Operation Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.read.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.function.write.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000258, "visualization.name": "Queued Requests", "visualization.description": "AzureFunction Queued Requests", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.request.queued.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000259, "visualization.name": "HTTP 5XX Error Details", "visualization.description": "AzureFunction HTTP 5XX Error Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.5xx.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000260, "visualization.name": "Memory Utilization", "visualization.description": "AzureFunction Memory Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.memory.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.function.private.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000261, "visualization.name": "App Domain Details", "visualization.description": "AzureFunction App Domain Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.app.domains", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.function.unloaded.app.domains", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000262, "visualization.name": "Garbage Collection Details", "visualization.description": "AzureFunction Garbage Collection Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.gen.0.collections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.function.gen.1.collections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.function.gen.2.collections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000263, "visualization.name": "Throughput Details", "visualization.description": "AzureFunction Throughput Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.function.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]