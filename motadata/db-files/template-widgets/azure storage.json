[{"_type": "0", "id": 10000000001439, "visualization.name": "Availability", "visualization.description": "AzureStorage Availability", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.storage.availability.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.storage.availability.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.storage.availability.percent", "icon": {"name": "stopwatch", "placement": "prefix"}}, "header": {"title": "Availability", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.storage.availability.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000001440, "visualization.name": "Transactions", "visualization.description": "AzureStorage Transactions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.storage.transactions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.storage.transactions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.storage.transactions", "icon": {"name": "transactions", "placement": "prefix"}}, "header": {"title": "Transactions", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.storage.transactions.last"}]}}}}, {"_type": "0", "id": 10000000001441, "visualization.name": "Blobs", "visualization.description": "AzureStorage Blobs", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.storage.blobs", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.storage.blobs"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.storage.blobs", "icon": {"name": "blob", "placement": "prefix"}}, "header": {"title": "Blobs", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.storage.blobs.last"}]}}}}, {"_type": "0", "id": 10000000001442, "visualization.name": "Queues", "visualization.description": "AzureStorage Queues", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.storage.queues", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.storage.queues"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.storage.queues", "icon": {"name": "queue", "placement": "prefix"}}, "header": {"title": "Queues", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.storage.queues.last"}]}}}}, {"_type": "0", "id": 10000000001443, "visualization.name": "File Shares", "visualization.description": "AzureStorage File Shares", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.storage.fileshares", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.storage.fileshares"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.storage.fileshares", "icon": {"name": "files", "placement": "prefix"}}, "header": {"title": "File Shares", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.storage.fileshares.last"}]}}}}, {"_type": "0", "id": 10000000001444, "visualization.name": "Tables", "visualization.description": "AzureStorage Tables", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.storage.tables", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.storage.tables"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.storage.tables", "icon": {"name": "table", "placement": "prefix"}}, "header": {"title": "Tables", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.storage.tables.last"}]}}}}, {"_type": "0", "id": 10000000001445, "visualization.name": "Latency", "visualization.description": "AzureStorage Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.storage.request.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001446, "visualization.name": "Queue Latency", "visualization.description": "AzureStorage Queue Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.storage.queue.server.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.storage.queue.e2e.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001447, "visualization.name": "Table Latency", "visualization.description": "AzureStorage Table Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.storage.table.e2e.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.storage.table.server.success.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001448, "visualization.name": "Top Queues By Transactions", "visualization.description": "AzureStorage Top Queues By Transactions", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.storage.queue.transactions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "azure.storage.queue.transactions.avg"}}}}, {"_type": "0", "id": 10000000001449, "visualization.name": "Top File Share By Quota Bytes", "visualization.description": "AzureStorage Top File Share By Quota Bytes", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.storage.fileshare~quota.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "azure.storage.fileshare~quota.bytes.avg"}}}}, {"_type": "0", "id": 10000000001450, "visualization.name": "Top Blobs By Bytes", "visualization.description": "AzureStorage Top Blobs By Bytes", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.storage.blob~size.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "azure.storage.blob~size.bytes.avg"}}}}, {"_type": "0", "id": **************, "visualization.name": "Blob Details", "visualization.description": "AzureStorage Blob Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.storage.blob~account.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.storage.blob~container.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.storage.blob~resource.group", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.storage.blob~location", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.storage.blob~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.storage.blob~size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "azure.storage.blob", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "azure.storage.blob~account.name.last", "title": "Account Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"classes": ["font-bold", "text-primary"]}}, {"name": "azure.storage.blob~container.name.last", "title": "Container Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"], "icon": {"name": "clone", "placement": "prefix", "classes": ["text-secondary-orange"]}}}, {"name": "azure.storage.blob~resource.group.last", "title": "Resource Group", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-bold"], "icon": {"name": "layer-group", "placement": "prefix", "classes": ["text-secondary-yellow"]}}}, {"name": "azure.storage.blob~location.last", "title": "Location", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-bold"], "icon": {"name": "flow-source", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "azure.storage.blob~type.last", "title": "Blob Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"classes": ["font-bold"], "icon": {"name": "folders", "placement": "prefix", "classes": ["text-secondary-green"]}}}, {"name": "azure.storage.blob~size.bytes.last", "title": "Blob Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"classes": ["font-bold"], "icon": {"name": "columns", "placement": "prefix", "classes": ["text-secondary-yellow"]}}}]}}}, {"_type": "0", "id": 10000000001452, "visualization.name": "Queue Details", "visualization.description": "AzureStorage Queue Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.storage.queue~url", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "azure.storage.queue", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold", "text-primary"]}}, {"name": "azure.storage.queue~url.last", "title": "Queue URL", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"classes": ["font-bold", "text-neutral-light"], "icon": {"name": "folders", "placement": "prefix", "classes": ["text-primary"]}}}]}}}, {"_type": "0", "id": 10000000001453, "visualization.name": "FileShare Details", "visualization.description": "AzureStorage FileShare Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.storage.fileshare~etag", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.storage.fileshare~last.modified", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.storage.fileshare~quota.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "azure.storage.fileshare", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold", "text-primary"]}}, {"name": "azure.storage.fileshare~quota.bytes.last", "title": "<PERSON><PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"classes": ["font-bold", "text-neutral-light"], "icon": {"name": "folders", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "azure.storage.fileshare~last.modified.last", "title": "Last Modified", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"], "icon": {"name": "stopwatch", "placement": "prefix", "classes": ["text-secondary-orange"]}}}, {"name": "azure.storage.fileshare~etag.last", "title": "FileShare eTag", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-bold"], "icon": {"name": "file-alt", "placement": "prefix", "classes": ["text-secondary-yellow"]}}}]}}}, {"_type": "0", "id": 10000000001454, "visualization.name": "Table Details", "visualization.description": "AzureStorage Table Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.storage.table~url", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "azure.storage.table", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold", "text-primary"]}}, {"name": "azure.storage.table~url.last", "title": "Table URL", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"classes": ["font-bold", "text-neutral-light"], "icon": {"name": "folders", "placement": "prefix", "classes": ["text-primary"]}}}]}}}]