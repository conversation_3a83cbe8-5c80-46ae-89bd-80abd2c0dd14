[{"_type": "0", "id": 10000000001925, "visualization.name": "Connection", "visualization.description": "MariaDB Connection", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.aborted.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.opened.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mariadb.aborted.connections"}, {"type": "metric", "data.point": "mariadb.connections"}, {"type": "metric", "data.point": "mariadb.opened.connections"}]}], "visualization.properties": {"gauge": {"header": {"title": "Connection", "style": {"font.size": "medium"}, "data.points": [{"label": "Aborted Connections", "value": "mariadb.aborted.connections.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Total", "value": "mariadb.connections.last"}, {"label": "Opened", "value": "mariadb.opened.connections.last"}]}, "style": {"icon": {"name": "active-connections"}, "color.data.point": "mariadb.aborted.connections"}}}}, {"_type": "0", "id": 10000000001926, "visualization.name": "<PERSON><PERSON><PERSON>", "visualization.description": "MariaDB Thread", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.running.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.created.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.connected.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mariadb.delayed.insert.threads"}, {"type": "metric", "data.point": "mariadb.slow.launch.threads"}, {"type": "metric", "data.point": "mariadb.running.threads"}, {"type": "metric", "data.point": "mariadb.created.threads"}, {"type": "metric", "data.point": "mariadb.connected.threads"}]}], "visualization.properties": {"gauge": {"header": {"title": "<PERSON><PERSON><PERSON>", "style": {"font.size": "medium"}, "data.points": [{"label": "Running Threads", "value": "mariadb.running.threads.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Created", "value": "mariadb.created.threads.last"}, {"label": "Connected", "value": "mariadb.connected.threads.last"}]}, "style": {"icon": {"name": "queue"}, "color.data.point": "mariadb.running.threads"}}}}, {"_type": "0", "id": 10000000001927, "visualization.name": "Task Performance", "visualization.description": "MariaDB Task Performance", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.open.tables", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.questions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.slow.queries", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mysql.table.lock.waits"}, {"type": "metric", "data.point": "mysql.table.immediate.locks"}, {"type": "metric", "data.point": "mariadb.open.tables"}, {"type": "metric", "data.point": "mariadb.questions"}, {"type": "metric", "data.point": "mariadb.slow.queries"}]}], "visualization.properties": {"gauge": {"header": {"title": "Task Performance", "style": {"font.size": "medium"}, "data.points": [{"label": "MariaDB Open Tables", "value": "mariadb.open.tables.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Questions", "value": "mariadb.questions.last"}, {"label": "Slow Queries", "value": "mariadb.slow.queries.last"}]}, "style": {"icon": {"name": "chart-line"}, "color.data.point": "mariadb.running.threads"}}}}, {"_type": "0", "id": 10000000001928, "visualization.name": "<PERSON><PERSON>", "visualization.description": "MariaDB <PERSON>", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.cached.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.thread.cache.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.query.cache.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mariadb.query.cache.hit.ratio.percent"}, {"type": "metric", "data.point": "mariadb.query.cache.inserts"}, {"type": "metric", "data.point": "mariadb.query.cache.hits"}, {"type": "metric", "data.point": "mariadb.cached.threads"}, {"type": "metric", "data.point": "mariadb.thread.cache.size.bytes"}, {"type": "metric", "data.point": "mariadb.query.cache.size.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "<PERSON><PERSON>", "style": {"font.size": "medium"}, "data.points": [{"label": "MariaDB Cached Threads", "value": "mariadb.cached.threads.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "<PERSON><PERSON><PERSON>", "value": "mariadb.thread.cache.size.bytes.last"}, {"label": "Query", "value": "mariadb.query.cache.size.bytes.last"}]}, "style": {"icon": {"name": "cache"}, "color.data.point": "mariadb.cached.threads"}}}}, {"_type": "0", "id": 10000000001929, "visualization.name": "Key Efficiency", "visualization.description": "MariaDB Key Efficiency", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.key.buffer.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.key.read.requests.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.key.write.requests.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mariadb.key.hit.ratio.percent"}, {"type": "metric", "data.point": "mariadb.key.write.requests.per.sec"}, {"type": "metric", "data.point": "mariadb.key.buffer.size.bytes"}, {"type": "metric", "data.point": "mariadb.key.read.requests.per.sec"}]}], "visualization.properties": {"gauge": {"header": {"title": "Key Efficiency", "style": {"font.size": "medium"}, "data.points": [{"label": "MariaDB Key Buffer Size", "value": "mariadb.key.buffer.size.bytes.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "mariadb.key.read.requests.per.sec.last"}, {"label": "Write", "value": "mariadb.key.write.requests.per.sec.last"}]}, "style": {"icon": {"name": "key"}, "color.data.point": "mariadb.key.buffer.size.bytes"}}}}, {"_type": "0", "id": 10000000001930, "visualization.name": "InnoDB", "visualization.description": "MariaDB InnoDB", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.innodb.page.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.innodb.buffer.pool.read.requests.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.innodb.buffer.pool.write.requests.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mariadb.innodb.buffer.pool.read.requests.per.sec"}, {"type": "metric", "data.point": "mariadb.innodb.buffer.pool.write.requests.per.sec"}, {"type": "metric", "data.point": "mariadb.innodb.page.size.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "InnoDB", "style": {"font.size": "medium"}, "data.points": [{"label": "Buffer Pool Size", "value": "mariadb.innodb.page.size.bytes.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "mariadb.innodb.buffer.pool.read.requests.per.sec.last"}, {"label": "Write", "value": "mariadb.innodb.buffer.pool.write.requests.per.sec.last"}]}, "style": {"icon": {"name": "inno-db"}, "color.data.point": "mariadb.innodb.page.size.bytes"}}}}, {"_type": "0", "id": 10000000001931, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000001932, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"id": 10000000001933, "_type": "0", "visualization.name": "Query Rates", "visualization.description": "MariaDB Query Rates", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.delete.commands.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.select.commands.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.update.commands.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.insert.commands.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001934, "_type": "0", "visualization.name": "Operational Throughput", "visualization.description": "MariaDB Operational Throughput", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.innodb.data.fsyncs.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.innodb.data.reads.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.innodb.data.writes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001935, "_type": "0", "visualization.name": "Connection", "visualization.description": "MariaDB Connection", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.aborted.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.aborted.clients", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001936, "_type": "0", "visualization.name": "Table Locks", "visualization.description": "MariaDB Table Locks", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.table.immediate.locks", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.table.lock.waits", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001937, "_type": "0", "visualization.name": "Select Statement Scans", "visualization.description": "MariaDB Select Statement Scans", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.select.scans.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.select.range.checks.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.select.ranges.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.select.full.joins.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001938, "_type": "0", "visualization.name": "<PERSON><PERSON><PERSON>", "visualization.description": "MariaDB Thread", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.created.threads", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.running.threads", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.cached.threads", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001939, "_type": "0", "visualization.name": "Table Sorts", "visualization.description": "MariaDB Table Sorts", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.sort.merge.passes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.sort.ranges.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001940, "_type": "0", "visualization.name": "Task Performance", "visualization.description": "MariaDB Task Performance", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.questions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.slow.queries", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.open.tables", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001941, "_type": "0", "visualization.name": "Temp Objects", "visualization.description": "MariaDB Temp Objects", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.created.temp.disk.tables.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.created.temp.files.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.created.temp.tables.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001942, "_type": "0", "visualization.name": "Row Statistics", "visualization.description": "MariaDB Row Statistics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.deleted.rows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.inserted.rows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.updated.rows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001943, "visualization.name": "Process Details", "visualization.description": "MariaDB Process Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.process.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.process.host", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.process.state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.process.database", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.process.info", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.process.user", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.process.command", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.process.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mariadb.process.id", "title": "ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mariadb.process.state", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mariadb.process.host", "title": "Host", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mariadb.process.database", "title": "Database", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mariadb.process.info", "title": "Info", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mariadb.process.user", "title": "User", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mariadb.process.command", "title": "Command", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mariadb.process.time.ms", "title": "Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000001944, "_type": "0", "visualization.name": "Row Operations", "visualization.description": "MariaDB InnoDB Row Operations", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.innodb.read.rows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.innodb.inserted.rows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.innodb.update.rows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.innodb.deleted.rows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001945, "_type": "0", "visualization.name": "Row Lock", "visualization.description": "MariaDB InnoDB Row Lock", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.innodb.row.lock.waits", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.innodb.row.lock.time.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001946, "_type": "0", "visualization.name": "Buffer Pool I/O", "visualization.description": "MariaDB InnoDB Buffer Pool I/O", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.innodb.buffer.pool.pages", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.innodb.buffer.pool.reads.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001947, "_type": "0", "visualization.name": "Database I/O", "visualization.description": "MariaDB InnoDB Database I/O", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.innodb.data.reads.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.innodb.data.writes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.innodb.data.fsyncs.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.innodb.log.writes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001948, "_type": "0", "visualization.name": "Buffer Pool Requests", "visualization.description": "MariaDB InnoDB Buffer Pool Requests", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.innodb.buffer.pool.read.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.innodb.buffer.pool.write.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001949, "_type": "0", "visualization.name": "Buffer Pool Size", "visualization.description": "MariaDB InnoDB Buffer Pool Size", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.innodb.buffer.pool.pages.data", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.innodb.buffer.pool.free.pages", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.innodb.buffer.pool.dirty.pages", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.innodb.buffer.pool.misc.pages", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001950, "visualization.name": "Index Statistics", "visualization.description": "MariaDB InnoDB Index Statistics", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.index.database.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.index.table.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.index.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.index.read.rows", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.index.fetches", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mariadb.index.database.name.last", "title": "Database Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mariadb.index.table.name.last", "title": "Table Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mariadb.index.name.last", "title": "Index Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mariadb.index.read.rows.last", "title": "Rows Read", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mariadb.index.fetches.last", "title": "Fetches", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000001951, "visualization.name": "Unused Indexes", "visualization.description": "MariaDB InnoDB Unused Indexes", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.unused.index.database.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.unused.index.table.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.unused.index", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mariadb.unused.index.database.name.last", "title": "Database Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mariadb.unused.index.table.name.last", "title": "Table Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mariadb.unused.index.last", "title": "Index Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000001953, "visualization.name": "Top Excessive Slow Queries By Avg Execution Time", "visualization.description": "MariaDB Log Top Excessive Slow Queries By Avg Execution Time", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "", "title": "Top", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "", "title": "Execution Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "", "title": "Query", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000001954, "visualization.name": "Log Event By Error and <PERSON> Severity(last 24 hours)", "visualization.description": "MariaDB Log Event By Error and Panic Severity(last 24 hours)", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "", "title": "Date/Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "", "title": "Severity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "", "title": "Query", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000001955, "visualization.name": "Top User Authentication Failed", "visualization.description": "MariaDB Log Top User Authentication Failed", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "", "title": "User Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "", "title": "Database Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "", "title": "Count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000001956, "visualization.name": "Top User Authentication Success", "visualization.description": "MariaDB Top User Authentication Success", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "", "title": "User Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "", "title": "Database Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "", "title": "Count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000002808, "visualization.name": "Missing Indexes", "visualization.description": "MariaDB InnoDB Missing Indexes", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mariadb.missing.index.database.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.missing.index.table.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.missing.index.column.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mariadb.missing.index", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mariadb.missing.index.database.name.last", "title": "Database Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mariadb.missing.index.table.name.last", "title": "Table Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mariadb.missing.index.last", "title": "Index Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mariadb.missing.index.column.name.last", "title": "Column Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}]