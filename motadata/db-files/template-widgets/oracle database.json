[{"_type": "0", "id": 10000000000639, "visualization.name": "Sessions", "visualization.description": "Oracle Sessions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.active.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.connected.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.blocking.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "oracle.active.sessions"}, {"type": "metric", "data.point": "oracle.connected.sessions"}, {"type": "metric", "data.point": "oracle.blocking.sessions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "sessions", "placement": "prefix"}}, "header": {"title": "Sessions", "style": {"font.size": "medium"}, "data.points": [{"label": "Connected", "value": "oracle.connected.sessions.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active", "value": "oracle.active.sessions.last"}, {"label": "Blocked", "value": "oracle.blocking.sessions.last"}]}}}}, {"_type": "0", "id": 10000000000640, "visualization.name": "SGA", "visualization.description": "Oracle SGA", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.sga.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.sga.memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.sga.memory.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "oracle.fixed.sga.used.bytes"}, {"type": "metric", "data.point": "oracle.sga.memory.free.bytes"}, {"type": "metric", "data.point": "oracle.shared.io.pool.used.bytes"}, {"type": "metric", "data.point": "oracle.shared.pool.used.bytes"}, {"type": "metric", "data.point": "oracle.sql.area.used.bytes"}, {"type": "metric", "data.point": "oracle.sga.used.percent"}, {"type": "metric", "data.point": "oracle.sga.memory.used.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "SGA", "style": {"font.size": "medium"}, "data.points": [{"label": "Used", "value": "oracle.sga.used.percent.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Used", "value": "oracle.sga.memory.used.bytes.last"}, {"label": "Free", "value": "oracle.sga.memory.free.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000641, "visualization.name": "PGA", "visualization.description": "Oracle PGA", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.pga.allocated.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.pga.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.pga.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "oracle.pga.processes"}, {"type": "metric", "data.point": "oracle.pga.max.processes"}, {"type": "metric", "data.point": "oracle.pga.cache.hit.ratio.percent"}, {"type": "metric", "data.point": "oracle.pga.used.percent"}, {"type": "metric", "data.point": "oracle.pga.allocated.bytes"}, {"type": "metric", "data.point": "oracle.pga.used.bytes"}, {"type": "metric", "data.point": "oracle.pga.free.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "PGA", "style": {"font.size": "medium"}, "data.points": [{"label": "Allocated Bytes", "value": "oracle.pga.allocated.bytes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Used", "value": "oracle.pga.used.bytes.last"}, {"label": "Free", "value": "oracle.pga.free.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000642, "visualization.name": "<PERSON><PERSON>", "visualization.description": "Oracle Cache", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.buffer.cache.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.library.cache.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.cursor.hit.ratio.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "oracle.library.cache.used.bytes"}, {"type": "metric", "data.point": "oracle.buffer.cache.used.bytes"}, {"type": "metric", "data.point": "oracle.data.dictionary.cache.hit.ratio.percent"}, {"type": "metric", "data.point": "oracle.sql.cache.hit.ratio.percent"}, {"type": "metric", "data.point": "oracle.cursor.miss.ratio.percent"}, {"type": "metric", "data.point": "oracle.cursor.hit.ratio.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "cache", "placement": "prefix"}}, "header": {"title": "<PERSON><PERSON>", "style": {"font.size": "medium"}, "data.points": [{"label": "Cursor Hit Ratio", "value": "oracle.cursor.hit.ratio.percent.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "<PERSON><PERSON><PERSON>", "value": "oracle.buffer.cache.used.bytes.last"}, {"label": "Library", "value": "oracle.library.cache.used.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000643, "visualization.name": "Database I/O", "visualization.description": "Oracle Database I/O", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.database.physical.reads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.database.physical.writes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.database.allocated.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "oracle.avg.executions"}, {"type": "metric", "data.point": "oracle.consistent.gets"}, {"type": "metric", "data.point": "oracle.database.block.gets"}, {"type": "metric", "data.point": "oracle.database.block.used.bytes"}, {"type": "metric", "data.point": "oracle.database.physical.reads"}, {"type": "metric", "data.point": "oracle.database.physical.writes"}, {"type": "metric", "data.point": "oracle.database.allocated.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "database", "placement": "prefix"}}, "header": {"title": "Database I/O", "style": {"font.size": "medium"}, "data.points": [{"label": "Database Size", "value": "oracle.database.allocated.bytes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "oracle.database.physical.reads.last"}, {"label": "Write", "value": "oracle.database.physical.writes.last"}]}}}}, {"_type": "0", "id": 10000000000644, "visualization.name": "Transactions", "visualization.description": "Oracle Transactions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.enqueue.waits", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.user.commits", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.user.rollbacks", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "oracle.session.commits"}, {"type": "metric", "data.point": "oracle.user.calls"}, {"type": "metric", "data.point": "oracle.enqueue.timeouts"}, {"type": "metric", "data.point": "oracle.enqueue.deadlocks"}, {"type": "metric", "data.point": "oracle.enqueue.requests"}, {"type": "metric", "data.point": "oracle.enqueue.waits"}, {"type": "metric", "data.point": "oracle.user.commits"}, {"type": "metric", "data.point": "oracle.user.rollbacks"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "transactions", "placement": "prefix"}}, "header": {"title": "Transactions", "style": {"font.size": "medium"}, "data.points": [{"label": "Enqueue Waits", "value": "oracle.enqueue.waits.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Commits", "value": "oracle.user.commits.last"}, {"label": "Rollbacks", "value": "oracle.user.rollbacks.last"}]}}}}, {"_type": "0", "id": 10000000000645, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000646, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000647, "visualization.name": "Database Details", "visualization.description": "Oracle Database Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.database.physical.reads", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.database.physical.writes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000648, "visualization.name": "Query Details", "visualization.description": "Oracle Query Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.query.elapsed.seconds", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000649, "visualization.name": "SGA & PGA Performance", "visualization.description": "Oracle SGA & PGA Performance", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.sga.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.pga.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000650, "visualization.name": "Transactions", "visualization.description": "Oracle Transactions Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.user.commits", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.user.rollbacks", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000651, "visualization.name": "Buffer <PERSON><PERSON>", "visualization.description": "Oracle Buffer <PERSON><PERSON>", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.buffer.cache.hit.ratio.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000652, "visualization.name": "Enqueue Timeouts", "visualization.description": "Oracle Enqueue Timeouts", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.enqueue.timeouts", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000653, "visualization.name": "SGA Pool", "visualization.description": "Oracle SGA Pool", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.shared.pool.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.large.pool.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.java.pool.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.streams.pool.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000654, "visualization.name": "Table Scans", "visualization.description": "Oracle Table Scans", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.table.scans", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000655, "visualization.name": "Session Details", "visualization.description": "Oracle Session Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.active.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.blocked.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.idle.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.connected.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000656, "visualization.name": "Sorts", "visualization.description": "Oracle Sorts", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.disk.sorts", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.memory.sorts", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000657, "visualization.name": "Tablespaces Utilization", "visualization.description": "Oracle Tablespaces Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "HorizontalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.table.space~utilization.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "10", "order": "desc", "column": "oracle.table.space~utilization.percent.avg"}}}}, {"_type": "0", "id": 10000000002216, "visualization.name": "Slow Running Queries", "visualization.description": "Oracle Slow Running Queries", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.query~elapsed.seconds", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "10", "order": "desc", "column": "oracle.query~elapsed.seconds.avg"}}}}, {"_type": "0", "id": 10000000000658, "visualization.name": "ASM Disk Details", "visualization.description": "Oracle ASM Disk Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.asm.disk.group~number", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.asm.disk.group~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.asm.disk.group~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.asm.disk.group~provisioned.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.asm.disk.group~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.asm.disk.group~free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "oracle.asm.disk.group~number.last", "title": "Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "oracle.asm.disk.group", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "oracle.asm.disk.group~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "oracle.asm.disk.group~state.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "oracle.asm.disk.group~provisioned.bytes.last", "title": "Total Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "oracle.asm.disk.group~used.bytes.last", "title": "Used Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "oracle.asm.disk.group~free.bytes.last", "title": "Free Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}]}}}, {"_type": "0", "id": 10000000000659, "visualization.name": "Oracle Job Details", "visualization.description": "Oracle Oracle Job Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.job~current.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.job~last.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.job~enable", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.job~fails", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.job~retries", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.job~executions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.job~next.execution.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.job~last.execution.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.job~elapsed.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "oracle.job", "title": "Job", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "oracle.job~current.status.last", "title": "Current Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "oracle.job~last.status.last", "title": "Last Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "oracle.job~enable.last", "title": "Enable", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "oracle.job~fails.last", "title": "Fails", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "oracle.job~retries.last", "title": "Retries", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "oracle.job~executions.last", "title": "Job Execution", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "oracle.job~next.execution.time.last", "title": "Next Execution Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}, {"name": "oracle.job~last.execution.time.last", "title": "Last Execution Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {}}, {"name": "oracle.job~elapsed.time.seconds.last", "title": "Elapsed Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11, "style": {}}]}}}, {"_type": "0", "id": 10000000000660, "visualization.name": "Oracle Rollback Segment", "visualization.description": "Oracle Oracle Rollback Segment", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.rollback.segment~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rollback.segment~shrinks", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rollback.segment~extends", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rollback.segment~hit.ratio.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rollback.segment~table.space", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rollback.segment~current.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rollback.segment~initial.extent.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rollback.segment~next.extent.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "oracle.rollback.segment", "title": "Segment", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "oracle.rollback.segment~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "oracle.rollback.segment~shrinks.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "oracle.rollback.segment~extends.last", "title": "Extends", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "oracle.rollback.segment~hit.ratio.percent.last", "title": "Hit Ratio", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "oracle.rollback.segment~table.space.last", "title": "Tablespace", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "oracle.rollback.segment~current.size.bytes.last", "title": "Current Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "oracle.rollback.segment~initial.extent.size.bytes.last", "title": "Initial Extent", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}, {"name": "oracle.rollback.segment~next.extent.size.bytes.last", "title": "Next Extent", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {}}]}}}, {"_type": "0", "id": 10000000000661, "visualization.name": "Oracle Session Details", "visualization.description": "Oracle Oracle Session Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.session.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.session.query", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.session.logon.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.session.cpu.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.session.cursors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.session.commits", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.session.physical.reads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.session.duration", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "oracle.session", "title": "Session", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "oracle.session.id", "title": "PID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "oracle.session.status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "oracle.session.query.last", "title": "Query", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "oracle.session.logon.time.last", "title": "Logon Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "oracle.session.cpu.time.ms.last", "title": "CPU Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "oracle.session.cursors.last", "title": "Cursors", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "oracle.session.commits.last", "title": "Commits", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}, {"name": "oracle.session.physical.reads.last", "title": "Physical Reads", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {}}, {"name": "oracle.session.duration.last", "title": "Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11, "style": {}}]}}}, {"_type": "0", "id": 10000000000662, "visualization.name": "Oracle Slowest Queries", "visualization.description": "Oracle Slowest Queries", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.query", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.query.sql.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.query.username", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.query.executions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.query.start.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.query.elapsed.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "oracle.query", "title": "Query", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "oracle.query.sql.id.last", "title": "SQL ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "oracle.query.username.last", "title": "Username", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "oracle.query.executions.last", "title": "Execution", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "oracle.query.start.time.last", "title": "Start Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "oracle.query.elapsed.seconds.last", "title": "Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}]}}}, {"_type": "0", "id": 10000000000663, "visualization.name": "Data File Details", "visualization.description": "Oracle Data File Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.data.file~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.data.file~tablespace", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.data.file~size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.data.file~read.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.data.file~write.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.data.file", "title": "Data File", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.data.file~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.data.file~tablespace.last", "title": "Tablespace", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.data.file~size.bytes.last", "title": "File Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.data.file~read.time.ms.last", "title": "Read Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.data.file~write.time.ms.last", "title": "Write Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000664, "visualization.name": "Tablespace Details", "visualization.description": "Oracle Tablespace Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.table.space~utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.table.space~data.files", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.table.space~free.blocks", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.table.space~read.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.table.space~write.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.table.space~size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.table.space~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.table.space", "title": "Tablespace", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.table.space~utilization.percent.last", "title": "Utilization", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.table.space~data.files.last", "title": "Data Files", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.table.space~free.blocks.last", "title": "Free Blocks", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.table.space~read.time.ms.last", "title": "Read TIme", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.table.space~write.time.ms.last", "title": "Write Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.table.space~size.bytes.last", "title": "Total Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.table.space~used.bytes.last", "title": "Used Space", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000665, "visualization.name": "Datafile", "visualization.description": "Oracle Datafile", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.data.files", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "large"}}}}, {"_type": "0", "id": 10000000000666, "visualization.name": "Tablespaces", "visualization.description": "Oracle Tablespaces", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.table.spaces", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "large"}}}}, {"_type": "0", "id": 10000000002214, "visualization.name": "Temporary Tablespace Details", "visualization.description": "Oracle Temporary Tablespace Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.table.spaces", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.temp.table.space.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.temp.table.space.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.temp.table.space.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.temp.table.space.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.table.spaces.last", "title": "Tablespace Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.temp.table.space.free.bytes.last", "title": "Free Blocks", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.temp.table.space.used.bytes.last", "title": "Used Blocks", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.temp.table.space.size.bytes.last", "title": "Space Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.temp.table.space.used.percent.last", "title": "Usage (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000002215, "visualization.name": "Table Details", "visualization.description": "Oracle Table Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.table", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.table.space.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.table.partitioned", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.table.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.table.rows", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.table.last", "title": "Table Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.table.space.name.last", "title": "Tablespace", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.table.partitioned.last", "title": "Parititioned", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.table.size.bytes.last", "title": "Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.table.rows.last", "title": "Rows", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000667, "visualization.name": "Index Details", "visualization.description": "Oracle Index Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.index.table.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.index.column.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.index.index.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.index.uniqueness", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.index.table.name.last", "title": "Table Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.index", "title": "Index Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.index.column.name.last", "title": "Column Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.index.index.type.last", "title": "Index Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.index.uniqueness.last", "title": "Uniqueness", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000668, "visualization.name": "Unused Indexes", "visualization.description": "Oracle Unused Indexes", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.unused.index.owner", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.unused.index.table.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.unused.index.idle.time.days", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.unused.index.rows", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.unused.index.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.unused.index.constraint.name", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.unused.index", "title": "Index Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.unused.index.owner.last", "title": "User", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.unused.index.table.name.last", "title": "Table Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.unused.index.idle.time.days.last", "title": "Days Without Usage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.unused.index.rows.last", "title": "Rows", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.unused.index.size.bytes.last", "title": "Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.unused.index.constraint.name.last", "title": "Constraint Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000669, "visualization.name": "RAC Status Details", "visualization.description": "Oracle RAC Status Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.rac.instance~database.version", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.instance~host.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.instance~status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.rac.instance~database.version.last", "title": "Database Version", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.rac.instance~host.name.last", "title": "Host Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.rac.instance~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}]