[{"visualization.name": "Ruckus Wireless Alert Overview", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "policy", "category": "metric", "visualization.result.by": ["severity"], "filters": {"data.filter": {}}, "severity": ["DOWN", "CRITICAL", "MAJOR", "CLEAR", "WARNING"], "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "Group", "entities": [10000000000022]}]}], "visualization.properties": {"gauge": {"style": {"layout": "Radial View", "chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "small", "text.align": "left", "color.conditions": [{"color": "#f04e3e", "value": 0}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 1585017354807}, {"visualization.name": "Ruckus Wireless Clients", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.time.range.inclusive": "no", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.clients", "aggregator": "avg"}]}], "visualization.properties": {"gauge": {"style": {"chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "small", "text.align": "center", "color.conditions": [{"color": "#f04e3e", "value": 0}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "granularity": {"value": 5, "unit": "m"}, "_type": "0", "id": 320155317405, "visualization.result.by": [], "container.type": "dashboard"}, {"visualization.name": "Ruckus Wireless Rogue APs", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.time.range.inclusive": "no", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.rogue.access.points", "aggregator": "avg"}]}], "visualization.properties": {"gauge": {"style": {"chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "small", "text.align": "center", "color.conditions": [{"color": "#f04e3e", "value": 0}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "granularity": {"value": 5, "unit": "m"}, "_type": "0", "id": 320155317406, "visualization.result.by": [], "container.type": "dashboard"}, {"visualization.name": "Ruckus Wireless Active Wlans", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.active.wlans", "aggregator": "last"}]}], "visualization.properties": {"gauge": {"style": {"chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "small", "text.align": "center", "color.conditions": [{"color": "#f04e3e", "value": 0}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 592802685656807}, {"visualization.name": "Ruckus Wireless APs", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.access.points", "aggregator": "avg"}]}], "visualization.properties": {"gauge": {"style": {"chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "small", "text.align": "center", "color.conditions": [{"color": "#f04e3e", "value": 0}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 592802685656813}, {"visualization.name": "Top Ruckus Wireless Client by Traffic", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["ruckus.wireless.client"], "data.points": [{"data.point": "ruckus.wireless.client~traffic.sent.bytes", "aggregator": "avg"}, {"data.point": "ruckus.wireless.client~traffic.received.bytes", "aggregator": "avg"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": "Monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ruckus.wireless.client", "title": "Client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "ruckus.wireless.client~traffic.sent.bytes.avg", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "ruckus.wireless.client~traffic.received.bytes.avg", "title": "Received Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "object.type", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "object.vendor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}], "sorting": {"limit": 10, "order": "desc", "column": "ruckus.wireless.client~traffic.sent.bytes.avg"}}}, "visualization.result.by": ["ruckus.wireless.client"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 592802685656816}, {"visualization.name": "Top Ruckus Wireless Client by Connection Retries", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["ruckus.wireless.client"], "data.points": [{"data.point": "ruckus.wireless.client~retries", "aggregator": "avg"}, {"data.point": "ruckus.wireless.client~ip.address", "aggregator": "last"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "ruckus.wireless.client", "title": "Client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ruckus.wireless.client~ip.address.last", "title": "Client IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "monitor", "title": "Monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "ruckus.wireless.client~retries.avg", "title": "Retry Count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "object.type", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "object.vendor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}], "sorting": {"limit": 10, "order": "desc", "column": "ruckus.wireless.client~retries.avg"}}}, "visualization.result.by": ["ruckus.wireless.client"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 592802685656823}, {"visualization.name": "Top Ruckus Wireless Client by Signal Strength", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["ruckus.wireless.client"], "data.points": [{"data.point": "ruckus.wireless.client~snr", "aggregator": "avg"}, {"data.point": "ruckus.wireless.client~signal.strength.dbm", "aggregator": "avg"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "ruckus.wireless.client", "title": "Client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "monitor", "title": "Monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "ruckus.wireless.client~snr.avg", "title": "SNR", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "ruckus.wireless.client~signal.strength.dbm.avg", "title": "Signal Strength", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "object.type", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "object.vendor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}], "sorting": {"limit": 10, "order": "asc", "column": "ruckus.wireless.client~snr.avg"}}}, "visualization.result.by": ["ruckus.wireless.client"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 592802685656824}, {"visualization.name": "Ruckus Wireless Rogue AP Channel", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["ruckus.wireless.rogue.access.point"], "data.points": [{"data.point": "ruckus.wireless.rogue.access.point~channel", "aggregator": "avg"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": "Monitor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {"width.percent": 32}}, {"name": "ruckus.wireless.rogue.access.point", "title": "Rogue AP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"width.percent": 38}}, {"name": "ruckus.wireless.rogue.access.point~channel.avg", "title": "Rogue AP Channel", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"width.percent": 32}}, {"name": "object.type", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "object.vendor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}]}}, "visualization.result.by": ["ruckus.wireless.rogue.access.point"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 592802685656826}, {"visualization.name": "Ruckus Wireless Wlan Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["ruckus.wireless.wlan"], "data.points": [{"data.point": "ruckus.wireless.wlan~clients", "aggregator": "avg"}, {"data.point": "ruckus.wireless.wlan~id", "aggregator": "avg"}, {"data.point": "ruckus.wireless.wlan~access.vlan", "aggregator": "avg"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": "Monitor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {"width.percent": 19}}, {"name": "ruckus.wireless.wlan", "title": "Wireless Wlan", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"width.percent": 24}}, {"name": "ruckus.wireless.wlan~id.avg", "title": "Wlan <PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"width.percent": 16}}, {"name": "ruckus.wireless.wlan~access.vlan.avg", "title": "Access Vlan", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"width.percent": 19}}, {"name": "ruckus.wireless.wlan~clients.avg", "title": "<PERSON>lan <PERSON>s", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"width.percent": 19}}, {"name": "object.type", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "object.vendor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}]}}, "visualization.result.by": ["ruckus.wireless.wlan"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 592802685656827}, {"visualization.name": "Top Ruckus Wireless Wlan by Traffic", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["ruckus.wireless.wlan"], "data.points": [{"data.point": "ruckus.wireless.wlan~traffic.sent.bytes", "aggregator": "avg"}, {"data.point": "ruckus.wireless.wlan~traffic.received.bytes", "aggregator": "avg"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": "Monitor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ruckus.wireless.wlan", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "ruckus.wireless.wlan~traffic.sent.bytes.avg", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "ruckus.wireless.wlan~traffic.received.bytes.avg", "title": "Received Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "object.type", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "object.vendor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}], "sorting": {"limit": 10, "order": "desc", "column": "ruckus.wireless.wlan~traffic.sent.bytes.avg"}}}, "visualization.result.by": ["ruckus.wireless.wlan"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 592802685656829}, {"visualization.name": "Ruckus Wireless Disconnected APs", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.disconnected.access.points", "aggregator": "avg"}]}], "visualization.properties": {"gauge": {"style": {"chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "small", "text.align": "center", "color.conditions": [{"color": "#f04e3e", "operator": ">=", "value": 1}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 680305309747153}, {"visualization.name": "Ruckus Wireless Excellent Clients", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.excellent.clients", "aggregator": "avg"}]}], "visualization.properties": {"gauge": {"style": {"chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "small", "text.align": "center", "color.conditions": [{"color": "#f04e3e", "value": 0}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 680305309747154}, {"visualization.name": "Top Ruckus Wireless AP by CPU Percent", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["ruckus.wireless.access.point"], "data.points": [{"data.point": "ruckus.wireless.access.point~cpu.percent", "aggregator": "avg"}, {"data.point": "ruckus.wireless.access.point~cpu.percent", "aggregator": "sparkline"}]}], "visualization.properties": {"sparkline": "yes", "grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": "Monitor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ruckus.wireless.access.point", "title": "Wireless AP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "ruckus.wireless.access.point~cpu.percent.avg", "title": "CPU Percent", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "object.type", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "object.vendor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "ruckus.wireless.access.point~cpu.percent.sparkline", "title": "Sparkline", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "type": "sparkline", "style": {"inline.chart": {"type": "sparkarea", "color": "#F5BC18"}}}], "sorting": {"limit": 10, "order": "desc", "column": "ruckus.wireless.access.point~cpu.percent.avg"}}}, "visualization.result.by": ["ruckus.wireless.access.point"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 680305309747157}, {"visualization.name": "Top Ruckus Wireless AP by Traffic", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["ruckus.wireless.access.point"], "data.points": [{"data.point": "ruckus.wireless.access.point~received.bytes", "aggregator": "avg"}, {"data.point": "ruckus.wireless.access.point~sent.bytes", "aggregator": "avg"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": "Monitor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ruckus.wireless.access.point", "title": "AP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "ruckus.wireless.access.point~sent.bytes.avg", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "ruckus.wireless.access.point~received.bytes.avg", "title": "Received Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "object.type", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "object.vendor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}], "sorting": {"limit": 10, "order": "desc", "column": "ruckus.wireless.access.point~sent.bytes.avg"}}}, "visualization.result.by": ["ruckus.wireless.access.point"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 680305309747158}, {"visualization.name": "Top Ruckus Wireless AP by Client", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["ruckus.wireless.access.point"], "data.points": [{"data.point": "ruckus.wireless.access.point~clients", "aggregator": "avg"}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "ruckus.wireless.access.point~clients.avg"}}}, "visualization.result.by": ["ruckus.wireless.access.point"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 680305309747159}, {"visualization.name": "Ruckus Wireless Client Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["ruckus.wireless.client"], "data.points": [{"data.point": "ruckus.wireless.client~username", "aggregator": "last"}, {"data.point": "ruckus.wireless.client~ap", "aggregator": "last"}, {"data.point": "ruckus.wireless.client~wlan.name", "aggregator": "last"}, {"data.point": "ruckus.wireless.client~channel", "aggregator": "avg"}, {"data.point": "ruckus.wireless.client~os.type", "aggregator": "last"}, {"data.point": "ruckus.wireless.client~auth.status", "aggregator": "last"}, {"data.point": "ruckus.wireless.client~channelization", "aggregator": "avg"}, {"data.point": "ruckus.wireless.client~health", "aggregator": "last"}, {"data.point": "ruckus.wireless.client~started.time.seconds", "aggregator": "avg"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": "Monitor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {"width.percent": 12}}, {"name": "ruckus.wireless.client", "title": "Client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"width.percent": 13}}, {"name": "ruckus.wireless.client~username.last", "title": "Username", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "ruckus.wireless.client~ap.last", "title": "AP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"width.percent": 12}}, {"name": "ruckus.wireless.client~wlan.name.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"width.percent": 15}}, {"name": "ruckus.wireless.client~channel.avg", "title": "Channel", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"width.percent": 10}}, {"name": "ruckus.wireless.client~os.type.last", "title": "OS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"width.percent": 12}}, {"name": "ruckus.wireless.client~auth.status.last", "title": "Auth Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"width.percent": 12}}, {"name": "ruckus.wireless.client~channelization.avg", "title": "Channelization", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}, {"name": "ruckus.wireless.client~health.last", "title": "Health", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {"width.percent": 12}}, {"name": "ruckus.wireless.client~started.time.seconds.avg", "title": "Started time", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11, "style": {}}, {"name": "object.type", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12, "style": {}}, {"name": "object.vendor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 13, "style": {}}]}}, "visualization.result.by": ["ruckus.wireless.client"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 680305309747160}, {"visualization.name": "Ruckus Wireless AP Received Bytes", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["ruckus.wireless.access.point"], "data.points": [{"data.point": "ruckus.wireless.access.point~received.bytes", "aggregator": "avg"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "visualization.result.by": ["ruckus.wireless.access.point"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 680305309747290}, {"visualization.name": "Ruckus Wireless AP Sent Bytes", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["ruckus.wireless.access.point"], "data.points": [{"data.point": "ruckus.wireless.access.point~sent.bytes", "aggregator": "avg"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "visualization.result.by": ["ruckus.wireless.access.point"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 680305309747291}]