[{"_type": "0", "id": 1712655926633, "visualization.name": "Health Overview", "visualization.description": "Health Overview Details", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"group.by": "monitor", "type": "policy", "category": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "severity": ["CRITICAL", "DOWN", "MAJOR", "WARNING", "CLEAR", "UNREACHABLE", "UNKNOWN"], "visualization.result.by": ["severity"], "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "category", "entities": ["Cloud"]}]}], "visualization.properties": {"gauge": {"style": {"chart.legend": "yes", "chart.label": "no", "type": "number", "font.size": "small", "color.conditions": [{"color": "#f04e3e"}, {"color": "#f58518"}, {"color": "#f5bc18"}]}}}, "visualization.result.by": ["severity"], "container.type": "dashboard"}, {"_type": "0", "id": 1712655926634, "visualization.name": "Monitor Health By Severity", "visualization.description": "Monitor Health By Severity", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"group.by": "monitor", "type": "policy.stream", "category": "metric", "filters": {"data.filter": {}}, "visualization.result.by": ["severity", "object.type"], "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "category", "entities": ["Cloud"]}]}], "visualization.properties": {"grid": {"view": "AlertStackedVerticalBar"}}, "visualization.result.by": ["severity", "object.type"], "container.type": "dashboard"}, {"_type": "0", "id": 1712655926635, "visualization.name": "AWS", "visualization.description": "AWS", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "HeatMap", "visualization.data.sources": [{"type": "policy", "category": "metric", "filters": {"data.filter": {}}, "visualization.result.by": ["object.type"], "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "object.type", "entities": ["AWS ELB", "AWS Lambda", "AWS Auto Scaling", "AWS Elastic Beanstalk", "Amazon DynamoDB", "Amazon EBS", "Amazon EC2", "Amazon RDS", "Amazon S3", "Amazon SNS", "Amazon CloudFront", "Amazon SQS", "AWS Elastic Beanstalk", "Amazon DocumentDB"]}]}], "visualization.properties": {}, "visualization.result.by": ["object.type"], "container.type": "dashboard"}, {"_type": "0", "id": 1712655926636, "visualization.name": "Azure", "visualization.description": "Azure", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "HeatMap", "visualization.data.sources": [{"type": "policy", "category": "metric", "filters": {"data.filter": {}}, "visualization.result.by": ["object.type"], "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "object.type", "entities": ["Azure Service Bus", "Azure Load Balancer", "Azure PostgreSQL Server", "Azure Cosmos DB", "Azure SQL Database", "Azure Storage", "Azure VM", "Azure WebApp", "Azure Application Gateway", "Azure Function", "Azure MySQL Server", "Azure PostgreSQL Server", "Azure VM Scale Set", "Azure CDN"]}]}], "visualization.properties": {}, "visualization.result.by": ["object.type"], "container.type": "dashboard"}, {"_type": "0", "id": 1712655926637, "visualization.name": "Office 365", "visualization.description": "Office 365", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "HeatMap", "visualization.data.sources": [{"type": "policy", "category": "metric", "filters": {"data.filter": {}}, "visualization.result.by": ["object.type"], "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "object.type", "entities": ["Office 365"]}]}], "visualization.properties": {}, "visualization.result.by": ["object.type"], "container.type": "dashboard"}]