package com.mindarray.slo;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.TestUtil;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.SLOCycle;
import com.mindarray.api.SLOProfile;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.SLOCycleConfigStore;
import com.mindarray.store.SLOProfileConfigStore;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.io.File;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(180 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestSLOProcessor
{
    private static final Logger LOGGER = new Logger(TestSLOProcessor.class, GlobalConstants.MOTADATA_SLO, "Test SLO Processor");

    // Test data context
    private static final JsonObject context = new JsonObject();
    private static final long objectId = 12345L;
    private static final String instance = "test-instance";

    // SLO Processor instance and deployment
    private static SLOProcessor sloProcessor;
    private static String deploymentId;

    // Reflection fields
    private static Field sloDetailsField;
    private static Field sloCyclesField;
    private static Field availabilitySLOProfilesField;
    private static Field sloEventsField;

    // Calculation methods via reflection
    private static Method getSLOAchievedPercentageMethod;
    private static Method getSLOViolatedSecondsMethod;
    private static Method getSLOMeanTimeToResolveMethod;
    private static Method getSLOMeanTimeBetweenFailureMethod;
    private static Method getSLOErrorBudgetLeftPercentageMethod;
    private static Method getSLOErrorBudgetLeftSecondsMethod;
    private static Method getSLOBurnRateSecondsMethod;
    private static Method getSLOBurnRateMethod;
    private static Method getSLOStatusMethod;
    private static Method updateSLOMethod;
    private static Method calculateMethod;
    private static Method loadMethod;
    private static Method dumpMethod;

    // Cycle management methods via reflection
    private static Method qualifyCyclesMethod;



    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            // Get existing SLO profiles created by TestSLOProfile
            var monitorAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "MonitorAvailabilitySLO");
            var monitorPerformanceSLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "MonitorPerformanceSLO");
            var instanceAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "InstanceAvailabilitySLO");
            var instancePerformanceSLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "InstancePerformanceSLO-Monthly-Filter-Process");

            // Store profile IDs for testing
            context.put("monitorAvailabilityId", monitorAvailabilitySLO.getLong(ID));
            context.put("monitorPerformanceId", monitorPerformanceSLO.getLong(ID));
            context.put("instanceAvailabilityId", instanceAvailabilitySLO.getLong(ID));
            context.put("instancePerformanceId", instancePerformanceSLO.getLong(ID));

            // Create and deploy SLOProcessor verticle
            sloProcessor = new SLOProcessor();

            TestUtil.vertx().undeploy(Bootstrap.getDeployedVerticles().remove(SLOProcessor.class.getSimpleName()), undeployResult ->
            {
                if (undeployResult.succeeded())
                {
                    LOGGER.info("SLO Processor verticle undeployed successfully");

                    TestUtil.vertx().deployVerticle(sloProcessor, deployResult ->
                    {
                        if (deployResult.succeeded())
                        {
                            deploymentId = deployResult.result();

                            try
                            {
                                Bootstrap.getDeployedVerticles().put(SLOProcessor.class.getSimpleName(), deploymentId);

                                // Setup reflection access to private fields and methods after deployment
                                setupReflectionFields();
                                setupReflectionMethods();

                                LOGGER.info("SLO Processor verticle deployed successfully with ID: " + deploymentId);

                                testContext.completeNow();
                            }
                            catch (Exception exception)
                            {
                                LOGGER.warn("Failed to setup reflection");

                                LOGGER.error(exception);

                                testContext.failNow(exception.getMessage());
                            }
                        }
                        else
                        {
                            LOGGER.warn("Failed to deploy SLO Processor verticle");

                            LOGGER.error(deployResult.cause());

                            testContext.failNow(deployResult.cause().getMessage());
                        }
                    });
                }
                else
                {
                    LOGGER.warn("Failed to undeploy SLO Processor verticle");

                    LOGGER.error(undeployResult.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("Running test case: %s", testInfo.getTestMethod().get().getName()));
        testContext.completeNow();
    }

    // ======================== Setup Helper Methods ========================

    /**
     * Setup reflection access to private fields of SLOProcessor
     */
    private static void setupReflectionFields() throws Exception
    {
        sloDetailsField = SLOProcessor.class.getDeclaredField("sloDetails");
        sloDetailsField.setAccessible(true);

        sloCyclesField = SLOProcessor.class.getDeclaredField("sloCycles");
        sloCyclesField.setAccessible(true);

        availabilitySLOProfilesField = SLOProcessor.class.getDeclaredField("availabilitySLOProfiles");
        availabilitySLOProfilesField.setAccessible(true);

        sloEventsField = SLOProcessor.class.getDeclaredField("sloEvents");
        sloEventsField.setAccessible(true);
    }

    /**
     * Setup reflection access to private methods of SLOProcessor
     */
    private static void setupReflectionMethods() throws Exception
    {
        getSLOAchievedPercentageMethod = SLOProcessor.class.getDeclaredMethod("getSLOAchievedPercentage", JsonObject.class, JsonObject.class);
        getSLOAchievedPercentageMethod.setAccessible(true);

        getSLOViolatedSecondsMethod = SLOProcessor.class.getDeclaredMethod("getSLOViolatedSeconds", JsonObject.class, JsonObject.class);
        getSLOViolatedSecondsMethod.setAccessible(true);

        getSLOMeanTimeToResolveMethod = SLOProcessor.class.getDeclaredMethod("getSLOMeanTimeToResolve", JsonObject.class, JsonObject.class);
        getSLOMeanTimeToResolveMethod.setAccessible(true);

        getSLOMeanTimeBetweenFailureMethod = SLOProcessor.class.getDeclaredMethod("getSLOMeanTimeBetweenFailure", JsonObject.class, JsonObject.class, long.class);
        getSLOMeanTimeBetweenFailureMethod.setAccessible(true);

        getSLOErrorBudgetLeftPercentageMethod = SLOProcessor.class.getDeclaredMethod("getSLOErrorBudgetLeftPercentage", JsonObject.class, JsonObject.class);
        getSLOErrorBudgetLeftPercentageMethod.setAccessible(true);

        getSLOErrorBudgetLeftSecondsMethod = SLOProcessor.class.getDeclaredMethod("getSLOErrorBudgetLeftSeconds", JsonObject.class, JsonObject.class);
        getSLOErrorBudgetLeftSecondsMethod.setAccessible(true);

        getSLOBurnRateSecondsMethod = SLOProcessor.class.getDeclaredMethod("getSLOBurnRateSeconds", JsonObject.class, JsonObject.class);
        getSLOBurnRateSecondsMethod.setAccessible(true);

        getSLOBurnRateMethod = SLOProcessor.class.getDeclaredMethod("getSLOBurnRate", long.class, JsonObject.class);
        getSLOBurnRateMethod.setAccessible(true);

        getSLOStatusMethod = SLOProcessor.class.getDeclaredMethod("getSLOStatus", JsonObject.class, JsonObject.class, int.class, int.class);
        getSLOStatusMethod.setAccessible(true);

        updateSLOMethod = SLOProcessor.class.getDeclaredMethod("updateSLO", JsonObject.class, SLOConstants.SLOFlapStatus.class, long.class, long.class, String.class, long.class, long.class, boolean.class);
        updateSLOMethod.setAccessible(true);

        // Cycle management methods
        qualifyCyclesMethod = SLOProcessor.class.getDeclaredMethod("qualifyCycles", JsonArray.class, AtomicInteger.class);
        qualifyCyclesMethod.setAccessible(true);

        calculateMethod = SLOProcessor.class.getDeclaredMethod("calculate");
        calculateMethod.setAccessible(true);

        loadMethod = SLOProcessor.class.getDeclaredMethod("load");
        loadMethod.setAccessible(true);

        dumpMethod = SLOProcessor.class.getDeclaredMethod("dump");
        dumpMethod.setAccessible(true);
    }

    // ======================== SLO Cycle Management Tests ========================

    /**
     * Test SLO cycle qualification with expired cycles
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testSLOCycleQualificationStartNewCycle(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");

            var monitorPerformanceId =  context.getLong("monitorPerformanceId");

            var instancePerformanceId =  context.getLong("instancePerformanceId");

            var sloProfiles = new JsonArray()
                    .add(SLOProfileConfigStore.getStore().getItem(monitorAvailabilityId))
                    .add(SLOProfileConfigStore.getStore().getItem(monitorPerformanceId))
                    .add(SLOProfileConfigStore.getStore().getItem(instancePerformanceId));

            var index = new AtomicInteger(0);

            // Get initial cycles count
            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(sloProcessor);
            var initialCyclesCount = sloCyclesMap.size();

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);
            var initialDetailsSize = sloDetailsMap.size();

            TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(1), v ->
            {
                try
                {
                    // Test cycle qualification
                    qualifyCyclesMethod.invoke(sloProcessor, sloProfiles, index);

                    var count = new AtomicInteger(0);

                    TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(5), timer ->
                    {
                        if (sloCyclesMap.size() > initialCyclesCount)
                        {
                            assertNotNull(sloCyclesMap.get(monitorAvailabilityId));

                            assertNotNull(sloCyclesMap.get(monitorPerformanceId));

                            assertNotNull(sloCyclesMap.get(instancePerformanceId));

                            var entityMap = sloDetailsMap.get(monitorAvailabilityId);

                            assertTrue(sloDetailsMap.size() >= initialDetailsSize, "SLO details map should grow");

                            assertNotNull(entityMap, "Entity map should be created");

                            entityMap = sloDetailsMap.get(monitorPerformanceId);

                            assertNotNull(entityMap, "Entity map should be created");

                            entityMap = sloDetailsMap.get(instancePerformanceId);

                            assertNotNull(entityMap, "Entity map should be created");

                            LOGGER.info(String.format("%s: start new cycle qualification test successful", testInfo.getTestMethod().get().getName()));

                            testContext.completeNow();

                            TestUtil.vertx().cancelTimer(timer);
                        }
                        else if (count.incrementAndGet() > 2)
                        {
                            testContext.failNow("new cycle failed to start!");

                            TestUtil.vertx().cancelTimer(timer);
                        }
                    });
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception.getMessage());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO cycle qualification with active cycles
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testSLOCycleQualificationActiveCycles(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");

            var sloProfiles = new JsonArray().add(SLOProfileConfigStore.getStore().getItem(monitorAvailabilityId));

            var index = new AtomicInteger(0);

            // Get initial cycles count
            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(sloProcessor);
            var initialCyclesCount = sloCyclesMap.size();

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);
            var initialDetailsSize = sloDetailsMap.size();

            var existingCycleId = sloCyclesMap.get(monitorAvailabilityId);

            // Test cycle qualification
            qualifyCyclesMethod.invoke(sloProcessor, sloProfiles, index);

            TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), timer ->
            {
                // Verify that active cycle is not restarted
                assertEquals(initialCyclesCount, sloCyclesMap.size(), "Active cycle should not be restarted");
                assertEquals(existingCycleId, sloCyclesMap.get(monitorAvailabilityId).longValue(),
                        "Existing cycle ID should remain unchanged");

                assertEquals(initialDetailsSize, sloDetailsMap.size(), "SLO details map should grow");

                LOGGER.info(String.format("%s: Active cycle qualification test successful", testInfo.getTestMethod().get().getName()));

                testContext.completeNow();
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== SLO Calculation Tests ========================

    /**
     * Test SLO achieved percentage calculation
     * Formula: (duration left * 100) / total duration
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testSLOAchievedPercentageCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            // Create test data with known values
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 8640L); // 86.4% of 10000 seconds left

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 10000L); // Total duration 10000 seconds

            // Expected: (8640 * 100) / 10000 = 86.4%
            var expectedPercentage = 86.4f;

            var actualPercentage = (Float) getSLOAchievedPercentageMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedPercentage, actualPercentage, 0.01f, "SLO achieved percentage calculation failed");

            LOGGER.info(String.format("%s: Expected: %.2f%%, Actual: %.2f%%", testInfo.getTestMethod().get().getName(), expectedPercentage, actualPercentage));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO violated seconds calculation
     * Formula: total duration - duration left
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testSLOViolatedSecondsCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 7200L); // 2 hours left

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L); // 24 hours total

            // Expected: 86400 - 7200 = 79200 seconds violated
            var expectedViolatedSeconds = 79200L;

            var actualViolatedSeconds = (Long) getSLOViolatedSecondsMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedViolatedSeconds, actualViolatedSeconds, "SLO violated seconds calculation failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedViolatedSeconds, actualViolatedSeconds));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test Mean Time To Resolve (MTTR) calculation
     * Formula: violated seconds / down incident count
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testSLOMeanTimeToResolveCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 82800L) // 23 hours left
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 4); // 4 down incidents

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L); // 24 hours total

            // Violated seconds: 86400 - 82800 = 3600 seconds
            // MTTR: 3600 / 4 = 900 seconds (15 minutes per incident)
            var expectedMTTR = 900L;

            var actualMTTR = (Long) getSLOMeanTimeToResolveMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedMTTR, actualMTTR, "MTTR calculation failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedMTTR, actualMTTR));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test MTTR calculation with zero incidents (edge case)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testSLOMeanTimeToResolveWithZeroIncidents(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L) // Full duration left
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 0); // No incidents

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L);

            // Expected: 0 (no incidents means no MTTR)
            var expectedMTTR = 0L;

            var actualMTTR = (Long) getSLOMeanTimeToResolveMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedMTTR, actualMTTR, "MTTR calculation with zero incidents failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedMTTR, actualMTTR));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test Mean Time Between Failure (MTBF) calculation
     * Formula: (total duration - violated seconds) / up incident count
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testSLOMeanTimeBetweenFailureCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 82800L) // 23 hours left
                    .put(SLOConstants.UP_INCIDENT_COUNT, 3); // 3 up incidents

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L); // 24 hours total

            var duration = 86400L;

            // Violated seconds: 86400 - 82800 = 3600 seconds
            // Uptime: 86400 - 3600 = 82800 seconds
            // MTBF: 82800 / 3 = 27600 seconds (7.67 hours between failures)
            var expectedMTBF = 27600L;

            var actualMTBF = (Long) getSLOMeanTimeBetweenFailureMethod.invoke(sloProcessor, sloData, sloCycle, duration);

            assertEquals(expectedMTBF, actualMTBF, "MTBF calculation failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedMTBF, actualMTBF));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test Error Budget Left Percentage calculation
     * Formula: 100 - ((total duration - duration left) * 100 / acceptable violation time)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testSLOErrorBudgetLeftPercentageCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 85500L); // 900 seconds violated

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L) // 24 hours total
                    .put(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME, 8640L); // 10% error budget (2.4 hours)

            // Violated: 86400 - 85500 = 900 seconds
            // Error budget used: (900 * 100) / 8640 = 10.42%
            // Error budget left: 100 - 10.42 = 89.58%
            var expectedErrorBudgetLeft = 89.58f;

            var actualErrorBudgetLeft = (Float) getSLOErrorBudgetLeftPercentageMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedErrorBudgetLeft, actualErrorBudgetLeft, 0.01f, "Error budget left percentage calculation failed");

            LOGGER.info(String.format("%s: Expected: %.2f%%, Actual: %.2f%%", testInfo.getTestMethod().get().getName(), expectedErrorBudgetLeft, actualErrorBudgetLeft));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test Error Budget Left Seconds calculation
     * Formula: acceptable violation time - (total duration - duration left)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testSLOErrorBudgetLeftSecondsCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 84600L); // 1800 seconds violated

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L) // 24 hours total
                    .put(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME, 8640L); // 10% error budget

            // Violated: 86400 - 84600 = 1800 seconds
            // Error budget left: 8640 - 1800 = 6840 seconds
            var expectedErrorBudgetLeftSeconds = 6840L;

            var actualErrorBudgetLeftSeconds = (Long) getSLOErrorBudgetLeftSecondsMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedErrorBudgetLeftSeconds, actualErrorBudgetLeftSeconds, "Error budget left seconds calculation failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedErrorBudgetLeftSeconds, actualErrorBudgetLeftSeconds));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO Burn Rate Seconds calculation
     * Formula: current violated seconds - last violated seconds
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testSLOBurnRateSecondsCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 84600L) // Current: 1800 seconds violated
                    .put(SLOConstants.LAST_VIOLATED_SECONDS, 900L); // Previous: 900 seconds violated

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L);

            // Current violated: 86400 - 84600 = 1800 seconds
            // Burn rate: 1800 - 900 = 900 seconds burned since last calculation
            var expectedBurnRateSeconds = 900L;

            var actualBurnRateSeconds = (Long) getSLOBurnRateSecondsMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedBurnRateSeconds, actualBurnRateSeconds, "Burn rate seconds calculation failed");

            // Verify that last violated seconds is updated
            assertEquals(1800L, sloData.getLong(SLOConstants.LAST_VIOLATED_SECONDS).longValue(), "Last violated seconds should be updated");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedBurnRateSeconds, actualBurnRateSeconds));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO Burn Rate Percentage calculation
     * Formula: (burn rate seconds * 100) / total duration
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testSLOBurnRatePercentageCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var burnRateSeconds = 1800L; // 30 minutes burned

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L); // 24 hours total

            // Expected: (1800 * 100) / 86400 = 2.08%
            var expectedBurnRatePercentage = 2.08f;

            var actualBurnRatePercentage = (Float) getSLOBurnRateMethod.invoke(sloProcessor, burnRateSeconds, sloCycle);

            assertEquals(expectedBurnRatePercentage, actualBurnRatePercentage, 0.01f, "Burn rate percentage calculation failed");

            LOGGER.info(String.format("%s: Expected: %.2f%%, Actual: %.2f%%", testInfo.getTestMethod().get().getName(), expectedBurnRatePercentage, actualBurnRatePercentage));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO Status determination based on achieved percentage
     * OK: >= warning threshold
     * WARNING: >= target but < warning
     * BREACHED: < target
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testSLOStatusCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 10000L);

            var target = 90; // 90% target
            var warning = 95; // 95% warning

            // Test BREACHED status (< 90%)
            var sloDataBreached = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 8500L); // 85% achieved

            var statusBreached = (Integer) getSLOStatusMethod.invoke(sloProcessor, sloDataBreached, sloCycle, target, warning);
            assertEquals(SLOConstants.SLOStatus.BREACHED.ordinal(), statusBreached.intValue(), "BREACHED status calculation failed");

            // Test WARNING status (>= 90% but < 95%)
            var sloDataWarning = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 9200L); // 92% achieved

            var statusWarning = (Integer) getSLOStatusMethod.invoke(sloProcessor, sloDataWarning, sloCycle, target, warning);
            assertEquals(SLOConstants.SLOStatus.WARNING.ordinal(), statusWarning.intValue(), "WARNING status calculation failed");

            // Test OK status (>= 95%)
            var sloDataOk = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 9600L); // 96% achieved

            var statusOk = (Integer) getSLOStatusMethod.invoke(sloProcessor, sloDataOk, sloCycle, target, warning);
            assertEquals(SLOConstants.SLOStatus.OK.ordinal(), statusOk.intValue(), "OK status calculation failed");

            LOGGER.info(String.format("%s: BREACHED=%d, WARNING=%d, OK=%d", testInfo.getTestMethod().get().getName(), statusBreached, statusWarning, statusOk));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== SLO Update and State Management Tests ========================

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testAddDummyEntityForAvailabilitySLO(VertxTestContext testContext)
    {
        try
        {
            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L); // Full duration initially

            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");

            sloDetailsMap.get(monitorAvailabilityId).put(objectId + INSTANCE_SEPARATOR + instance, sloData);

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO update for first poll (initialization)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testSLOUpdateFirstPoll(VertxTestContext testContext)
    {
        try
        {
            var currentTimestamp = DateTimeUtil.currentSeconds();

            context.put("monitorAvailabilityTimestamp", currentTimestamp);

            var event = new JsonObject()
                    .put(SEVERITY, STATUS_UP)
                    .put(AIOpsConstants.ENTITY_ID, objectId)
                    .put(INSTANCE, instance)
                    .put(EventBusConstants.EVENT_TIMESTAMP, currentTimestamp);

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_SLO_UPDATE, event);

            var count = new AtomicInteger(0);

            TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(5), timer ->
            {
                var sloData = sloDetailsMap.get(monitorAvailabilityId).get(objectId + INSTANCE_SEPARATOR + instance);

                if (sloData.getInteger(SLOConstants.LAST_SLO_STATUS) != null)
                {
                    assertNotNull(sloData.getInteger(SLOConstants.LAST_SLO_STATUS), "Last SLO status should be set");
                    assertEquals(SLOConstants.SLOFlapStatus.HEALTHY.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Initial status should be HEALTHY");
                    assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_TIMESTAMP).longValue(), "Last timestamp should be set");
                    assertEquals(1, sloData.getInteger(SLOConstants.UP_INCIDENT_COUNT).intValue(), "Up incident count should be 1");

                    testContext.completeNow();

                    TestUtil.vertx().cancelTimer(timer);
                }
                else if (count.incrementAndGet() > 2)
                {
                    testContext.failNow("new cycle failed to start!");

                    TestUtil.vertx().cancelTimer(timer);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testSLOUpdateStatusChange(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var currentTimestamp = context.getLong("monitorAvailabilityTimestamp") + 10;

            context.put("monitorAvailabilityTimestamp", currentTimestamp);

            var event = new JsonObject()
                    .put(SEVERITY, STATUS_DOWN)
                    .put(AIOpsConstants.ENTITY_ID, objectId)
                    .put(INSTANCE, instance)
                    .put(EventBusConstants.EVENT_TIMESTAMP, currentTimestamp);

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_SLO_UPDATE, event);

            var count = new AtomicInteger(0);

            TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(5), timer ->
            {
                var sloData = sloDetailsMap.get(monitorAvailabilityId).get(objectId + INSTANCE_SEPARATOR + instance);

                if (sloData.getInteger(SLOConstants.LAST_BREACHED_TIMESTAMP) != null)
                {
                    assertEquals(SLOConstants.SLOFlapStatus.DEGRADED.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Status should change to DEGRADED");
                    assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_TIMESTAMP).longValue(), "Last timestamp should be updated");
                    assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_BREACHED_TIMESTAMP).longValue(), "Last breached timestamp should be set");
                    assertEquals(1, sloData.getInteger(SLOConstants.DOWN_INCIDENT_COUNT).intValue(), "Down incident count should increment");

                    LOGGER.info(String.format("%s: Status change from HEALTHY to DEGRADED successful", testInfo.getTestMethod().get().getName()));

                    testContext.completeNow();

                    TestUtil.vertx().cancelTimer(timer);
                }
                else if (count.incrementAndGet() > 2)
                {
                    testContext.failNow("new cycle failed to start!");

                    TestUtil.vertx().cancelTimer(timer);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testSLOUpdateContinuousDegradedState(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var currentTimestamp = context.getLong("monitorAvailabilityTimestamp") + 60;

            context.put("monitorAvailabilityTimestamp", currentTimestamp);

            var event = new JsonObject()
                    .put(SEVERITY, STATUS_DOWN)
                    .put(AIOpsConstants.ENTITY_ID, objectId)
                    .put(INSTANCE, instance)
                    .put(EventBusConstants.EVENT_TIMESTAMP, currentTimestamp);

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_SLO_UPDATE, event);

            var count = new AtomicInteger(0);

            TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(5), timer ->
            {
                var sloData = sloDetailsMap.get(monitorAvailabilityId).get(objectId + INSTANCE_SEPARATOR + instance);

                if (sloData.getInteger(SLOConstants.LAST_BREACHED_TIMESTAMP) != null)
                {
                    var expectedDurationLeft = 86400 - 60;
                    assertEquals(expectedDurationLeft, sloData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Duration left should be reduced by degraded time");
                    assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_BREACHED_TIMESTAMP).longValue(), "Last breached timestamp should be updated");

                    LOGGER.info(String.format("%s: Continuous degraded state tracking successful. Duration reduced by %d seconds",
                            testInfo.getTestMethod().get().getName(), 60));


                    testContext.completeNow();

                    TestUtil.vertx().cancelTimer(timer);
                }
                else if (count.incrementAndGet() > 2)
                {
                    testContext.failNow("new cycle failed to start!");

                    TestUtil.vertx().cancelTimer(timer);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testSLOUpdateDisableFromDegradedState(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var currentTimestamp = context.getLong("monitorAvailabilityTimestamp") + 60;

            context.put("monitorAvailabilityTimestamp", currentTimestamp);

            var event = new JsonObject()
                    .put(SEVERITY, STATUS_DISABLE)
                    .put(AIOpsConstants.ENTITY_ID, objectId)
                    .put(INSTANCE, instance)
                    .put(EventBusConstants.EVENT_TIMESTAMP, currentTimestamp);

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_SLO_UPDATE, event);

            var count = new AtomicInteger(0);

            TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(5), timer ->
            {
                var sloData = sloDetailsMap.get(monitorAvailabilityId).get(objectId + INSTANCE_SEPARATOR + instance);

                if (sloData.getInteger(SLOConstants.LAST_SLO_STATUS) == SLOConstants.SLOFlapStatus.NOT_CALCULATED.ordinal())
                {
                    var expectedDurationLeft = 86400 - 120;
                    assertEquals(expectedDurationLeft, sloData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Duration left should be reduced by degraded time");

                    testContext.completeNow();

                    TestUtil.vertx().cancelTimer(timer);
                }
                else if (count.incrementAndGet() > 2)
                {
                    testContext.failNow("new cycle failed to start!");

                    TestUtil.vertx().cancelTimer(timer);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testSLOUpdateRecoveryFromDegradedState(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var currentTimestamp = context.getLong("monitorAvailabilityTimestamp") + 60;

            context.put("monitorAvailabilityTimestamp", currentTimestamp);

            var event = new JsonObject()
                    .put(SEVERITY, STATUS_UP)
                    .put(AIOpsConstants.ENTITY_ID, objectId)
                    .put(INSTANCE, instance)
                    .put(EventBusConstants.EVENT_TIMESTAMP, currentTimestamp);

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_SLO_UPDATE, event);

            var count = new AtomicInteger(0);

            TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(5), timer ->
            {
                var sloData = sloDetailsMap.get(monitorAvailabilityId).get(objectId + INSTANCE_SEPARATOR + instance);

                if (sloData.getInteger(SLOConstants.LAST_SLO_STATUS) == SLOConstants.SLOFlapStatus.HEALTHY.ordinal())
                {
                    var expectedDurationLeft = 86400 - 120;
                    assertEquals(expectedDurationLeft, sloData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Duration left should be reduced by degraded time");

                    testContext.completeNow();

                    TestUtil.vertx().cancelTimer(timer);
                }
                else if (count.incrementAndGet() > 2)
                {
                    testContext.failNow("new cycle failed to start!");

                    TestUtil.vertx().cancelTimer(timer);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testGlobalSLOUpdateFirstPoll(VertxTestContext testContext)
    {
        try
        {
            var currentTimestamp = DateTimeUtil.currentSeconds();

            context.put("monitorPerformanceTimestamp", currentTimestamp);

            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(sloProcessor);

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var monitorPerformanceId = context.getLong("monitorPerformanceId");

            var sloData = sloDetailsMap.get(monitorPerformanceId).get(EMPTY_VALUE);

            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.DEGRADED, currentTimestamp, NOT_AVAILABLE, EMPTY_VALUE, monitorPerformanceId, sloCyclesMap.get(monitorPerformanceId), false);

            assertNotNull(sloData.getInteger(SLOConstants.LAST_SLO_STATUS), "Last SLO status should be set");
            assertEquals(SLOConstants.SLOFlapStatus.DEGRADED.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Initial status should be DEGRADED");
            assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_TIMESTAMP).longValue(), "Last timestamp should be set");
            assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_BREACHED_TIMESTAMP).longValue(), "Last breached timestamp should be set");
            assertEquals(1, sloData.getInteger(SLOConstants.DOWN_INCIDENT_COUNT).intValue(), "Down incident count should be 1");

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testGlobalSLOUpdateContinuousDegraded(VertxTestContext testContext)
    {
        try
        {
            var currentTimestamp = context.getLong("monitorPerformanceTimestamp") + 60;

            context.put("monitorPerformanceTimestamp", currentTimestamp);

            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(sloProcessor);

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var monitorPerformanceId = context.getLong("monitorPerformanceId");

            var sloData = sloDetailsMap.get(monitorPerformanceId).get(EMPTY_VALUE);

            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.DEGRADED, currentTimestamp, NOT_AVAILABLE, EMPTY_VALUE, monitorPerformanceId, sloCyclesMap.get(monitorPerformanceId), false);

            assertNotNull(sloData.getInteger(SLOConstants.LAST_SLO_STATUS), "Last SLO status should be set");
            assertEquals(SLOConstants.SLOFlapStatus.DEGRADED.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Initial status should be DEGRADED");
            assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_BREACHED_TIMESTAMP).longValue(), "Last breached timestamp should be set");
            assertEquals(1, sloData.getInteger(SLOConstants.DOWN_INCIDENT_COUNT).intValue(), "Down incident count should be 1");
            var expectedDurationLeft = TimeUnit.DAYS.toSeconds(30) - 60;
            assertEquals(expectedDurationLeft, sloData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Duration left should be reduced by degraded time");

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testGlobalSLOUpdateRecoveryFromDegradedState(VertxTestContext testContext)
    {
        try
        {
            var currentTimestamp = context.getLong("monitorPerformanceTimestamp") + 60;

            context.put("monitorPerformanceTimestamp", currentTimestamp);

            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(sloProcessor);

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var monitorPerformanceId = context.getLong("monitorPerformanceId");

            var sloData = sloDetailsMap.get(monitorPerformanceId).get(EMPTY_VALUE);

            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.HEALTHY, currentTimestamp, NOT_AVAILABLE, EMPTY_VALUE, monitorPerformanceId, sloCyclesMap.get(monitorPerformanceId), false);

            assertNotNull(sloData.getInteger(SLOConstants.LAST_SLO_STATUS), "Last SLO status should be set");
            assertEquals(SLOConstants.SLOFlapStatus.HEALTHY.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Initial status should be HEALTHY");
            assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_TIMESTAMP).longValue(), "Last timestamp should be set");
            var expectedDurationLeft = TimeUnit.DAYS.toSeconds(30) - 120;
            assertEquals(expectedDurationLeft, sloData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Duration left should be reduced by degraded time");

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testCalculateSLO(VertxTestContext testContext)
    {
        try
        {
            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            calculateMethod.invoke(sloProcessor);

            assertEquals(sloDetailsMap.get(context.getLong("monitorAvailabilityId")).get(objectId + INSTANCE_SEPARATOR + instance).getLong(SLOConstants.DURATION_LEFT),
                    sloDetailsMap.get(context.getLong("monitorAvailabilityId")).get(EMPTY_VALUE).getLong(SLOConstants.DURATION_LEFT));

            assertEquals(sloDetailsMap.get(context.getLong("monitorAvailabilityId")).get(objectId + INSTANCE_SEPARATOR + instance).getLong(SLOConstants.LAST_TIMESTAMP),
                    sloDetailsMap.get(context.getLong("monitorAvailabilityId")).get(EMPTY_VALUE).getLong(SLOConstants.LAST_TIMESTAMP));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== Cache File Dump and Load Tests ========================

    /**
     * Test SLO cache file dump functionality
     * Verifies that sloDetails and sloEvents are properly serialized to cache file
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testSLOCacheFileDump(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            // Get current state of maps
            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            // Add some test data to ensure we have content to dump
            var testSloId = context.getLong("monitorAvailabilityId");
            var testData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L)
                    .put(SLOConstants.LAST_TIMESTAMP, DateTimeUtil.currentSeconds())
                    .put(SLOConstants.LAST_SLO_STATUS, SLOConstants.SLOFlapStatus.HEALTHY.ordinal());

            sloDetailsMap.get(testSloId).put("test-dump-key", testData);

            // Trigger dump via reflection
            dumpMethod.invoke(sloProcessor);

            // Verify cache file exists
            var cacheFilePath = CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + SLOConstants.SLO_CACHE_FILE;
            var cacheFile = new File(cacheFilePath);

            assertTrue(cacheFile.exists(), "SLO cache file should exist after dump");
            assertTrue(cacheFile.length() > 0, "SLO cache file should not be empty");

            LOGGER.info(String.format("%s: Cache file dump successful. File size: %d bytes",
                    testInfo.getTestMethod().get().getName(), cacheFile.length()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO cache file load functionality
     * Verifies that sloDetails and sloEvents are properly deserialized from cache file
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testSLOCacheFileLoad(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            // Clear current maps to test loading
            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);
            var sloEventsMap = (Map<Long, Map<Long, Integer>>) sloEventsField.get(sloProcessor);

            var testSloId = context.getLong("monitorAvailabilityId");
            var originalTestData = sloDetailsMap.get(testSloId).get("test-dump-key");

            // Clear the maps
            sloDetailsMap.clear();
            sloEventsMap.clear();

            loadMethod.invoke(sloProcessor);

            // Verify data was loaded back
            assertTrue(sloDetailsMap.size() > 0, "SLO details should be loaded from cache file");
            assertNotNull(sloDetailsMap.get(testSloId), "Test SLO profile should be loaded");

            var loadedTestData = sloDetailsMap.get(testSloId).get("test-dump-key");
            assertNotNull(loadedTestData, "Test data should be loaded from cache");
            assertEquals(originalTestData.getLong(SLOConstants.DURATION_LEFT),
                    loadedTestData.getLong(SLOConstants.DURATION_LEFT), "Duration left should match");

            LOGGER.info(String.format("%s: Cache file load successful. Loaded %d SLO profiles",
                    testInfo.getTestMethod().get().getName(), sloDetailsMap.size()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO cache file load with non-existent file
     * Verifies that load method handles missing cache file gracefully
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testSLOCacheFileLoadWithMissingFile(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            // Delete cache file if it exists
            var cacheFilePath = CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + SLOConstants.SLO_CACHE_FILE;
            var cacheFile = new File(cacheFilePath);
            if (cacheFile.exists())
            {
                cacheFile.delete();
            }

            // Clear maps
            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);
            var sloEventsMap = (Map<Long, Map<Long, Integer>>) sloEventsField.get(sloProcessor);
            sloDetailsMap.clear();
            sloEventsMap.clear();

            loadMethod.invoke(sloProcessor);

            // Verify file was created and maps remain empty
            assertTrue(cacheFile.exists(), "Cache file should be created if it doesn't exist");
            assertEquals(0, sloDetailsMap.size(), "SLO details should be empty when loading from new file");
            assertEquals(0, sloEventsMap.size(), "SLO events should be empty when loading from new file");

            LOGGER.info(String.format("%s: Missing cache file handled gracefully. New file created.",
                    testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test periodic cache dump functionality
     * Verifies that cache is dumped periodically when updated flag is set
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testPeriodicCacheDump(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            // Get the updated field via reflection
            var updatedField = SLOProcessor.class.getDeclaredField("updated");
            updatedField.setAccessible(true);

            // Set updated flag to true
            updatedField.setBoolean(sloProcessor, true);

            // Get cache file modification time before
            var cacheFilePath = CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + SLOConstants.SLO_CACHE_FILE;
            var cacheFile = new File(cacheFilePath);
            var lastModifiedBefore = cacheFile.lastModified();

            // Wait for periodic timer to trigger (30 seconds interval)
            TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(35), timer ->
            {
                try
                {
                    // Verify file was updated
                    var lastModifiedAfter = cacheFile.lastModified();
                    assertTrue(lastModifiedAfter > lastModifiedBefore, "Cache file should be updated by periodic dump");

                    // Verify updated flag was reset
                    assertFalse(updatedField.getBoolean(sloProcessor), "Updated flag should be reset after dump");

                    LOGGER.info(String.format("%s: Periodic cache dump successful.",
                            testInfo.getTestMethod().get().getName()));

                    testContext.completeNow();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                    testContext.failNow(exception.getMessage());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== SLO Cycle Restart Tests ========================

    /**
     * Test SLO cycle restart when cycle end time is reached
     * Verifies that expired cycles are properly ended and new cycles are started
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testSLOCycleRestartOnExpiry(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");
            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(sloProcessor);
            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            // Get current cycle ID
            var currentCycleId = sloCyclesMap.get(monitorAvailabilityId);
            assertNotNull(currentCycleId, "Current cycle should exist");

            // Modify cycle end time to be in the past to trigger restart
            var currentCycle = SLOCycleConfigStore.getStore().getItem(currentCycleId);
            var pastEndTime = DateTimeUtil.currentSeconds() - 10; // 10 seconds ago
            currentCycle.put(SLOCycle.SLO_CYCLE_END_TIME, pastEndTime);

            // Update the cycle in store
            Bootstrap.configDBService().update(DBConstants.TBL_SLO_CYCLE,
                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, currentCycleId),
                    currentCycle, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, updateResult ->
                    {
                        if (updateResult.succeeded())
                        {
                            SLOCycleConfigStore.getStore().updateItem(currentCycleId).onComplete(storeResult ->
                            {
                                if (storeResult.succeeded())
                                {
                                    try
                                    {
                                        // Trigger cycle qualification
                                        var sloProfiles = new JsonArray().add(SLOProfileConfigStore.getStore().getItem(monitorAvailabilityId));
                                        qualifyCyclesMethod.invoke(sloProcessor, sloProfiles, new AtomicInteger(0));

                                        // Wait for cycle restart to complete
                                        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), timer ->
                                        {
                                            try
                                            {
                                                var newCycleId = sloCyclesMap.get(monitorAvailabilityId);
                                                assertNotNull(newCycleId, "New cycle should be created");
                                                assertNotEquals(currentCycleId, newCycleId, "New cycle ID should be different");

                                                // Verify SLO details were reset for new cycle
                                                var entityMap = sloDetailsMap.get(monitorAvailabilityId);
                                                assertNotNull(entityMap, "Entity map should exist for new cycle");

                                                LOGGER.info(String.format("%s: Cycle restart successful. Old cycle: %d, New cycle: %d",
                                                        testInfo.getTestMethod().get().getName(), currentCycleId, newCycleId));

                                                testContext.completeNow();
                                            }
                                            catch (Exception exception)
                                            {
                                                LOGGER.error(exception);
                                                testContext.failNow(exception.getMessage());
                                            }
                                        });
                                    }
                                    catch (Exception exception)
                                    {
                                        LOGGER.error(exception);
                                        testContext.failNow(exception.getMessage());
                                    }
                                }
                                else
                                {
                                    testContext.failNow("Failed to update cycle in store");
                                }
                            });
                        }
                        else
                        {
                            testContext.failNow("Failed to update cycle in database");
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO cycle end functionality
     * Verifies that cycle end calculations are performed correctly
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testSLOCycleEnd(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");
            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(sloProcessor);
            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            // Get current cycle
            var currentCycleId = sloCyclesMap.get(monitorAvailabilityId);
            var currentCycle = SLOCycleConfigStore.getStore().getItem(currentCycleId);
            var sloProfile = SLOProfileConfigStore.getStore().getItem(monitorAvailabilityId);

            // Add some test data to SLO details
            var testData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 82800L) // 23 hours left
                    .put(SLOConstants.LAST_TIMESTAMP, DateTimeUtil.currentSeconds())
                    .put(SLOConstants.LAST_SLO_STATUS, SLOConstants.SLOFlapStatus.HEALTHY.ordinal())
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 2)
                    .put(SLOConstants.UP_INCIDENT_COUNT, 3);

            sloDetailsMap.get(monitorAvailabilityId).put("test-end-cycle", testData);

            // Trigger endCycle via reflection
            var endCycleMethod = SLOProcessor.class.getDeclaredMethod("endCycle", JsonObject.class, JsonObject.class);
            endCycleMethod.setAccessible(true);
            endCycleMethod.invoke(sloProcessor, sloProfile, currentCycle);

            // Verify that end cycle calculations were performed
            // The method should calculate final SLO metrics and store them
            LOGGER.info(String.format("%s: Cycle end processing completed for cycle %d",
                    testInfo.getTestMethod().get().getName(), currentCycleId));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO profile deletion cleanup
     * Verifies that SLO data is properly cleaned up when profile is deleted
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testSLOProfileDeletionCleanup(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var testSloId = 99999L; // Use a test ID that won't conflict
            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);
            var sloEventsMap = (Map<Long, Map<Long, Integer>>) sloEventsField.get(sloProcessor);
            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(sloProcessor);
            var availabilitySLOProfilesList = (List<Long>) availabilitySLOProfilesField.get(sloProcessor);

            // Add test data to all maps
            sloDetailsMap.put(testSloId, new HashMap<>());
            sloEventsMap.put(testSloId, new TreeMap<>());
            sloCyclesMap.put(testSloId, 12345L);
            availabilitySLOProfilesList.add(testSloId);

            // Verify data exists
            assertTrue(sloDetailsMap.containsKey(testSloId), "Test SLO should exist in details map");
            assertTrue(sloEventsMap.containsKey(testSloId), "Test SLO should exist in events map");
            assertTrue(sloCyclesMap.containsKey(testSloId), "Test SLO should exist in cycles map");
            assertTrue(availabilitySLOProfilesList.contains(testSloId), "Test SLO should exist in availability profiles");

            // Send deletion event
            var deletionEvent = new JsonObject()
                    .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_SLO_PROFILE.name())
                    .put(ID, testSloId);

            TestUtil.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, deletionEvent);

            // Wait for cleanup to complete
            TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(5), timer ->
            {
                // Verify data was cleaned up
                assertFalse(sloDetailsMap.containsKey(testSloId), "Test SLO should be removed from details map");
                assertFalse(sloEventsMap.containsKey(testSloId), "Test SLO should be removed from events map");
                assertFalse(sloCyclesMap.containsKey(testSloId), "Test SLO should be removed from cycles map");
                assertFalse(availabilitySLOProfilesList.contains(testSloId), "Test SLO should be removed from availability profiles");

                LOGGER.info(String.format("%s: SLO profile deletion cleanup successful for ID %d",
                        testInfo.getTestMethod().get().getName(), testSloId));

                testContext.completeNow();
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test HA cache update notification
     * Verifies that cache update notifications are handled properly
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testHACacheUpdateNotification(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            // Set dirty flag via reflection
            var dirtyField = SLOProcessor.class.getDeclaredField("dirty");
            dirtyField.setAccessible(true);
            dirtyField.setBoolean(sloProcessor, true);

            // Send HA cache update notification
            var cacheUpdateEvent = new JsonObject()
                    .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_CACHE.name());

            TestUtil.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, cacheUpdateEvent);

            // Wait for processing
            TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(5), timer ->
            {
                try
                {
                    // Verify dirty flag was reset
                    assertFalse(dirtyField.getBoolean(sloProcessor), "Dirty flag should be reset after cache update");

                    LOGGER.info(String.format("%s: HA cache update notification handled successfully",
                            testInfo.getTestMethod().get().getName()));

                    testContext.completeNow();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                    testContext.failNow(exception.getMessage());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test cache file compression and decompression
     * Verifies that cache data is properly compressed and decompressed
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testCacheFileCompressionDecompression(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);
            var sloEventsMap = (Map<Long, Map<Long, Integer>>) sloEventsField.get(sloProcessor);

            // Add substantial test data to verify compression
            var testSloId = 88888L;
            var testDetails = new HashMap<String, JsonObject>();

            for (var i = 0; i < 100; i++)
            {
                var testData = new JsonObject()
                        .put(SLOConstants.DURATION_LEFT, 86400L - i)
                        .put(SLOConstants.LAST_TIMESTAMP, DateTimeUtil.currentSeconds() + i)
                        .put(SLOConstants.LAST_SLO_STATUS, i % 2)
                        .put(SLOConstants.DOWN_INCIDENT_COUNT, i / 10)
                        .put(SLOConstants.UP_INCIDENT_COUNT, i / 5);

                testDetails.put("test-key-" + i, testData);
            }

            sloDetailsMap.put(testSloId, testDetails);

            // Add test events
            var testEvents = new TreeMap<Long, Integer>();
            for (var i = 0; i < 50; i++)
            {
                testEvents.put(DateTimeUtil.currentSeconds() + i, i % 3);
            }
            sloEventsMap.put(testSloId, testEvents);

            // Dump and reload to test compression/decompression
            dumpMethod.invoke(sloProcessor);

            // Clear and reload
            sloDetailsMap.clear();
            sloEventsMap.clear();

            loadMethod.invoke(sloProcessor);

            // Verify data integrity after compression/decompression
            assertTrue(sloDetailsMap.containsKey(testSloId), "Test SLO should exist after reload");
            assertEquals(100, sloDetailsMap.get(testSloId).size(), "All test details should be preserved");
            assertTrue(sloEventsMap.containsKey(testSloId), "Test events should exist after reload");
            assertEquals(50, sloEventsMap.get(testSloId).size(), "All test events should be preserved");

            LOGGER.info(String.format("%s: Cache compression/decompression successful. Details: %d, Events: %d",
                    testInfo.getTestMethod().get().getName(),
                    sloDetailsMap.get(testSloId).size(),
                    sloEventsMap.get(testSloId).size()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        try
        {
            LOGGER.info("all test cases executed");

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }
}
