package com.mindarray.slo;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.*;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;

public class SLOProcessor extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(SLOProcessor.class, GlobalConstants.MOTADATA_SLO, "SLO Processor");

    /**
     * This map holds the different metadata for particular SLO profile.
     * <ul>
     *     <li>Key: slo.profile.id</li>
     *     <li>Value: Map
     *      <ul>
     *          <li>Key: object.id + ~ + instance</li>
     *          <li>Value: Map
     *              <ul>
     *                  <li>last.timestamp : last timestamp when slo status changed, we store this as we want duration when any status changes</li>
     *                  <li>last.breached.timestamp : last breached timestamp when the last breached status dumped, we store this as we continuously minus that from the duration left</li>
     *                  <li>last.slo.status : represents last slo status</li>
     *                  <li>last.violated.percent : stores last violated percentage, as we use this to calculate burn rate</li>
     *                  <li>duration.left : main counter which represents the left duration, which we use to calculate the achieved percentage, violated percentage, violated seconds, error budget left percentage, error budget left seconds</li>
     *                  <li>down.incident.count : represents how many times monitor became down (i.e. one incident), we use this to calculate MTTR</li>
     *                  <li>up.incident.count : represents how many times monitor became up (i.e. one incident), we use this to calculate MTBF</li>
     *              </ul>
     *          </li>
     *      </ul>
     *     </li>
     *
     * </ul>
     */
    private static final Map<Long, Map<String, JsonObject>> sloDetails = new HashMap<>();

    /**
     * This map holds the mapping of slo profile id to slo cycle id.
     * <ul>
     *     <li>Key: slo.profile.id</li>
     *     <li>Value: slo.cycle.id</li>
     * </ul>
     */
    private static final Map<Long, Long> sloCycles = new HashMap<>();

    private static final List<Long> availabilitySLOProfiles = new ArrayList<>();

    /**
     * This map holds the events for particular slo profile.
     * <ul>
     *     <li>Key: slo.profile.id</li>
     *     <li>Value: Map
     *      <ul>
     *          <li>Key: timestamp</li>
     *          <li>Value: count of down events</li>
     *      </ul>
     *     </li>
     * </ul>
     */
    private static final Map<Long, TreeMap<Long, Integer>> sloEvents = new HashMap<>();

    /**
     * This counter is used for managing different data dump for different slo frequencies.
     * like every 15 mins we call {@link #calculate()} method
     * <ul>
     *     <li>if count == 1: dump data for daily slo</li>
     *     <li>if count == 2: dump data for daily and weekly slo</li>
     *     <li>if count == 3: dump data for daily, weekly and monthly slo</li>
     *     <li>if count == 4: dump data for daily, weekly, monthly and quarterly slo</li>
     * </ul>
     */
    private static final AtomicInteger count = new AtomicInteger(1);

    /**
     * This flag is used to check if any slo details are updated or not.
     * We use this flag to dump the data to file.
     */
    private boolean updated = false;

    private boolean dirty = false;

    /**
     * these variables are used to store column mapper related updates
     */
    private final StringBuilder builder = new StringBuilder(0);

    private Set<String> mappers;

    // ------------------------ Verticle related methods ------------------------ //

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            // load the data from the file
            load();

            mappers = new HashSet<>();

            // store the mapping of slo profile id to slo cycle id
            var items = SLOProfileConfigStore.getStore().getActiveSLOProfiles();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (item.getString(SLOProfile.SLO_PROFILE_STATE).equalsIgnoreCase(YES)
                        && item.containsKey(SLOProfile.SLO_CYCLE_ID))
                {
                    sloCycles.put(item.getLong(ID), item.getLong(SLOProfile.SLO_CYCLE_ID));

                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug(String.format("slo cycles : %s", sloCycles));
                    }

                    if (item.getString(SLOProfile.SLO_PROFILE_TYPE).equalsIgnoreCase(SLOConstants.SLOType.AVAILABILITY.getName()))
                    {
                        availabilitySLOProfiles.add(item.getLong(ID));
                    }

                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug(String.format("availability slo profiles : %s", availabilitySLOProfiles));
                    }
                }
            }

            // cache flush related logic
            var refreshTimer = new AtomicInteger(MotadataConfigUtil.getSLOCacheFlushTimerSeconds());

            vertx.setPeriodic(30 * 1000L, timer ->
            {
                refreshTimer.addAndGet(-30);

                if (updated)
                {
                    dump();

                    dirty = true;

                    updated = false;
                }
                else if (refreshTimer.get() <= 0)
                {
                    dump();

                    dirty = true;

                    refreshTimer.set(MotadataConfigUtil.getSLOCacheFlushTimerSeconds());
                }
            });

            // at the given interval calculate the slo and dump calculated data to the db.
            vertx.setPeriodic(TimeUnit.MINUTES.toMillis(MotadataConfigUtil.getSLOCalculationTimerMinutes()), timer -> calculate());

            // we receive this event every day at 00:00:00, and we start and stop any cycles are qualified for starting and stopping
            vertx.eventBus().localConsumer(EventBusConstants.EVENT_SLO_CYCLE_QUALIFY, message -> qualifyCycles(SLOProfileConfigStore.getStore().getItems(), new AtomicInteger(0)));

            // we receive from MetricPolicyInspector based on which we update the SLO status and all it's metadata
            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_SLO_UPDATE, message -> process(message.body()));

            // HA cache dump related logic
            vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
            {
                try
                {
                    if (message.body().getString(CHANGE_NOTIFICATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(EventBusConstants.ChangeNotificationType.UPDATE_CACHE.name()))
                    {
                        if (dirty)
                        {
                            dirty = false;

                            HAConstants.notifyObserver(new JsonObject().put(HAConstants.CACHE_NAME, SLOConstants.SLO_CACHE_FILE).put(RESULT, vertx.fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + SLOConstants.SLO_CACHE_FILE)));
                        }
                    }
                    else if (message.body().getString(CHANGE_NOTIFICATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(ChangeNotificationType.DELETE_SLO_PROFILE.name()))
                    {
                        try
                        {
                            var id = message.body().getLong(ID);

                            sloDetails.remove(id);

                            sloEvents.remove(id);

                            availabilitySLOProfiles.remove(id);

                            sloCycles.remove(id);
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            promise.complete();
        }
        catch (Exception exception)
        {
            promise.fail(exception);

            LOGGER.error(exception);
        }
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        dump();

        promise.complete();
    }

    // ------------------------ Cache File related methods ------------------------ //

    /**
     * this method dumps the {@link #sloDetails} and {@link #sloEvents} to the slo-cache file
     */
    private void dump()
    {
        try
        {
            Bootstrap.vertx().fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + SLOConstants.SLO_CACHE_FILE,
                    Buffer.buffer(CodecUtil.compress(new JsonObject().put("items", sloDetails).put("events", sloEvents).encode().getBytes())));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * this method loads the {@link #sloDetails} and {@link #sloEvents} from the slo-cache file
     */
    private void load()
    {
        var file = new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + SLOConstants.SLO_CACHE_FILE);

        if (file.exists())
        {
            var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath());

            if (buffer != null && buffer.getBytes().length > 0)
            {
                var context = new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))).getMap();

                if (context.containsKey("items"))
                {
                    for (var entry : ((Map<String, Object>) context.get("items")).entrySet())
                    {
                        var key = entry.getKey();

                        var value = (Map<String, Object>) entry.getValue();

                        var map = new HashMap<String, JsonObject>();

                        for (var e: value.entrySet())
                        {
                            map.put(e.getKey(), JsonObject.mapFrom(e.getValue()));
                        }

                        sloDetails.put(CommonUtil.getLong(key), map);
                    }
                }

                if (context.containsKey("events"))
                {
                    for (var entry : ((Map<String, Object>) context.get("events")).entrySet())
                    {
                        var key = entry.getKey();

                        var value = (Map<String, Object>) entry.getValue();

                        var map = new TreeMap<Long, Integer>();

                        for (var e: value.entrySet())
                        {
                            map.put(CommonUtil.getLong(e.getKey()), CommonUtil.getInteger(e.getValue()));
                        }

                        sloEvents.put(CommonUtil.getLong(key), map);
                    }
                }

                LOGGER.info(String.format("%s loaded...", SLOConstants.SLO_CACHE_FILE));
            }
        }
        else
        {
            LOGGER.info(String.format("%s created...", SLOConstants.SLO_CACHE_FILE));

            Bootstrap.vertx().fileSystem().createFileBlocking(file.getPath());
        }
    }

    // ------------------------ SLO Cycle related methods ------------------------ //

    /**
     * This method is used to qualify the SLO cycles.
     * <p>
     *     We check if any cycle is ended or not, if ended we end the cycle and start a new cycle.
     *     We also check if any SLO profile is not having any cycle started yet, if not we start a new cycle.
     * </p>
     */
    private void qualifyCycles(JsonArray items, AtomicInteger index)
    {
        try
        {
            var currentTime = DateTimeUtil.currentSeconds() + 1; // adding 1 second to current time to avoid exact match

            LOGGER.info("qualifying slo cycles... " + currentTime);

            if (items.size() > index.get())
            {
                var item = items.getJsonObject(index.get());

                var context = item.getJsonObject(SLOProfile.SLO_PROFILE_CONTEXT);

                var diff = switch (SLOConstants.SLOFrequency.valueOfName(context.getString(SLOProfile.SLO_FREQUENCY)))
                {
                    case WEEKLY -> TimeUnit.DAYS.toSeconds(7);
                    case MONTHLY -> TimeUnit.DAYS.toSeconds(30);
                    case QUARTERLY -> TimeUnit.DAYS.toSeconds(90);
                    default -> TimeUnit.DAYS.toSeconds(1); // default we will take daily
                };

                // means already one cycle is running in this SLO profile
                if (item.containsKey(SLOProfile.SLO_CYCLE_ID))
                {
                    var cycle = SLOCycleConfigStore.getStore().getItem(item.getLong(SLOProfile.SLO_CYCLE_ID));

                    if (cycle != null && !cycle.isEmpty() && cycle.containsKey(SLOCycle.SLO_CYCLE_END_TIME))
                    {
                        var endTime = cycle.getLong(SLOCycle.SLO_CYCLE_END_TIME);

                        if (endTime != null && endTime <= currentTime)
                        {
                            LOGGER.info(String.format("for slo profile %s end time %s is less than current time %s", item.getString(SLOProfile.SLO_PROFILE_NAME), endTime, currentTime));

                            LOGGER.info(String.format("restarting new cycle for slo profile %s", item.getString(SLOProfile.SLO_PROFILE_NAME)));

                            // end the current cycle
                            endCycle(item, cycle);

                            startCycle(item, endTime, endTime + diff).onComplete(result->
                            {
                                index.set(index.incrementAndGet());

                                qualifyCycles(items, index);
                            });
                        }
                        else
                        {
                            LOGGER.info(String.format("not restarting new cycle for slo profile %s as end time %s is greater than current time %s", item.getString(SLOProfile.SLO_PROFILE_NAME), endTime, currentTime));

                            index.set(index.incrementAndGet());

                            qualifyCycles(items, index);
                        }
                    }
                }
                // means SLO profile is not having any cycle started yet
                else
                {
                    var startTime = item.getLong(SLOProfile.SLO_PROFILE_START_TIME);

                    if (startTime <= currentTime)
                    {
                        LOGGER.info(String.format("for slo profile %s start time %s is less than current time %s", item.getString(SLOProfile.SLO_PROFILE_NAME), startTime, currentTime));

                        LOGGER.info(String.format("starting new cycle for slo profile %s", item.getString(SLOProfile.SLO_PROFILE_NAME)));

                        // start a new cycle
                        startCycle(item, startTime, startTime + diff).onComplete(result->
                        {
                            index.set(index.incrementAndGet());

                            qualifyCycles(items, index);
                        });
                    }
                    else
                    {
                        LOGGER.info(String.format("not starting new cycle for slo profile %s as start time %s is greater than current time %s", item.getString(SLOProfile.SLO_PROFILE_NAME), startTime, currentTime));

                        index.set(index.incrementAndGet());

                        qualifyCycles(items, index);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void endCycle(JsonObject sloProfile, JsonObject sloCycle)
    {
        try
        {
            var sloId = sloProfile.getLong(ID);

            var sloInstances = sloDetails.get(sloId);

            var sloContext = sloProfile.getJsonObject(SLOProfile.SLO_PROFILE_CONTEXT);

            var timestamp = sloCycle.getLong(SLOCycle.SLO_CYCLE_END_TIME);

            var duration = sloCycle.getLong(SLOCycle.SLO_CYCLE_TOTAL_DURATION);

            LOGGER.info(String.format("ending cycle %s for slo profile %s", sloCycle.getLong(ID), sloId));

            int downCount = calculateInstanceSLO(sloInstances, sloId, sloCycle, sloContext, timestamp, duration, sloProfile.getString(SLOProfile.SLO_PROFILE_TYPE, EMPTY_VALUE).equalsIgnoreCase(SLOConstants.SLOType.AVAILABILITY.getName()));

            calculateGlobalSLO(sloId, sloCycle, sloContext, timestamp, duration, downCount, sloProfile.getString(SLOProfile.SLO_PROFILE_TYPE, EMPTY_VALUE).equalsIgnoreCase(SLOConstants.SLOType.AVAILABILITY.getName()));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * This method is used to start a new SLO cycle.
     * <p>
     *     We create a new cycle in the database and update the SLO profile with the new cycle id.
     *     We also send a notification to the database to start the new cycle.
     *     We also keep a mapping of SLO profile to cycle in the {@link #sloCycles} map.
     * </p>
     * @param sloProfile the SLO profile
     * @param startTime the start time of the cycle
     * @param endTime the end time of the cycle
     */
    private Future<Void> startCycle(JsonObject sloProfile, long startTime, long endTime)
    {
        var promise = Promise.<Void>promise();

        try
        {
            var sloId = sloProfile.getLong(ID);

            // clear the map
            sloDetails.remove(sloId);

            // clear the map
            sloEvents.remove(sloId);

            // remove from availability SLO profiles
            availabilitySLOProfiles.remove(sloId);

            // remove from slo cycles
            sloCycles.remove(sloId);

            var enabled = sloProfile.getString(SLOProfile.SLO_PROFILE_STATE).equalsIgnoreCase(YES);

            var cycleId = SLOCycleConfigStore.getStore().newId();

            var cycle = new JsonObject();

            var sloContext = sloProfile.getJsonObject(SLOProfile.SLO_PROFILE_CONTEXT);

            var duration = endTime - startTime;

            // resolve entities
            var entities = sloContext.containsKey(ENTITY_TYPE) && sloContext.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG)
                    ? ObjectConfigStore.getStore().getObjectIdsByTags(sloContext.getJsonArray(ENTITIES))
                    : sloContext.containsKey(ENTITY_TYPE) && sloContext.getString(ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName())
                    ? ObjectConfigStore.getStore().getIdsByGroups(sloContext.getJsonArray(ENTITIES))
                    : sloContext.getJsonArray(ENTITIES);

            LOGGER.info(String.format("entities for slo profile %s are %s", sloProfile.getString(SLOProfile.SLO_PROFILE_NAME), entities));

            cycle.put(ID, cycleId)
                    .put(SLOCycle.SLO_CYCLE_NAME, sloProfile.getString(SLOProfile.SLO_PROFILE_NAME) + SPACE_SEPARATOR + DateTimeUtil.timestamp(startTime * 1000))
                    .put(SLOCycle.SLO_PROFILE_ID, sloId)
                    .put(SLOCycle.SLO_CYCLE_START_TIME, startTime)
                    .put(SLOCycle.SLO_CYCLE_END_TIME, endTime)
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, duration)
                    .put(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME, duration - ((duration * sloContext.getInteger(SLOProfile.SLO_TARGET)) / 100))
                    .put(ENTITIES, entities);

            LOGGER.info(String.format("cycle : %s", cycle.encodePrettily()));

            sloProfile.put(SLOProfile.SLO_CYCLE_ID, cycleId);

            // update SLO profile
            Bootstrap.configDBService().update(DBConstants.TBL_SLO_PROFILE,
                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, sloId),
                    sloProfile, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                    {
                        if (result.succeeded())
                        {
                            SLOProfileConfigStore.getStore().updateItem(sloId).onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    Bootstrap.configDBService().save(DBConstants.TBL_SLO_CYCLE,
                                            cycle, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, future ->
                                            {
                                                if (future.succeeded())
                                                {
                                                    SLOCycleConfigStore.getStore().addItem(future.result()).onComplete(asyncFuture ->
                                                    {
                                                        if (asyncFuture.succeeded())
                                                        {
                                                            try
                                                            {
                                                                if (enabled)
                                                                {
                                                                    // send notification to db
                                                                    VisualizationConstants.sendCycleStartNotification(sloId.toString(), cycleId, startTime, LOGGER);

                                                                    LOGGER.info(String.format("new cycle %s started for slo profile %s with start time %s and end time %s", cycleId, sloId, startTime, endTime));

                                                                    // keep mapping of SLO profile to cycle
                                                                    sloCycles.put(sloId, cycleId);

                                                                    if (CommonUtil.debugEnabled())
                                                                    {
                                                                        LOGGER.debug(String.format("slo cycles : %s", sloCycles));
                                                                    }

                                                                    if (sloProfile.getString(SLOProfile.SLO_PROFILE_TYPE).equalsIgnoreCase(SLOConstants.SLOType.AVAILABILITY.getName()))
                                                                    {
                                                                        availabilitySLOProfiles.add(sloProfile.getLong(ID));

                                                                        if (CommonUtil.debugEnabled())
                                                                        {
                                                                            LOGGER.debug(String.format("availability slo profiles : %s", availabilitySLOProfiles));
                                                                        }
                                                                    }

                                                                    sloDetails.put(sloId, new HashMap<>());

                                                                    sloEvents.put(sloId, new TreeMap<>());

                                                                    // start the SLO cycle
                                                                    vertx.eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                                                                            .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.SLO_CYCLE_START)
                                                                            .put(SLOProfile.SLO_CYCLE_ID, cycleId)
                                                                            .put(SLOCycle.SLO_PROFILE_ID, sloId));

                                                                    qualifyAndAssignEntities(sloProfile, entities, startTime, duration);

                                                                    promise.complete();
                                                                }
                                                                else
                                                                {
                                                                    promise.complete();
                                                                }
                                                            }
                                                            catch (Exception exception)
                                                            {
                                                                LOGGER.error(exception);

                                                                promise.fail(exception);
                                                            }
                                                        }
                                                        else
                                                        {
                                                            LOGGER.error(asyncFuture.cause());

                                                            promise.fail(asyncFuture.cause());
                                                        }
                                                    });
                                                }
                                                else
                                                {
                                                    LOGGER.error(future.cause());

                                                    promise.fail(future.cause());
                                                }
                                            });
                                }
                                else
                                {
                                    LOGGER.error(asyncResult.cause());

                                    promise.fail(asyncResult.cause());
                                }
                            });
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            promise.fail(result.cause());
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    /**
     * This method is used to assign the SLO to the entities.
     * <p>
     *     We check if the SLO is for instances or not, if yes we get the instances for the entities.
     *     We then check the status of the instances and update the SLO accordingly.
     *     We also update the global SLO status based on the instance status.
     * </p>
     * @param sloProfile the SLO profile
     * @param objects the objects
     * @param startTime the start time of the cycle
     * @param duration the duration of the cycle
     */
    private void qualifyAndAssignEntities(JsonObject sloProfile, JsonArray objects, long startTime, long duration)
    {
        try
        {
            LOGGER.info(String.format("qualifying and assigning entities for slo profile %s", sloProfile.getString(SLOProfile.SLO_PROFILE_NAME)));

            var sloId = sloProfile.getLong(ID);

            var cycleId = sloCycles.get(sloId);

            var sloContext = sloProfile.getJsonObject(SLOProfile.SLO_PROFILE_CONTEXT);

            var availabilitySLO = sloProfile.getString(SLOProfile.SLO_PROFILE_TYPE).equalsIgnoreCase(SLOConstants.SLOType.AVAILABILITY.getName());

            var sloInstance = sloContext.getString(SLOProfile.SLO_INSTANCE, EMPTY_VALUE);

            SLOConstants.SLOFlapStatus globalSLOStatus = SLOConstants.SLOFlapStatus.HEALTHY;

            var detail = sloDetails.get(sloId);

            for (var index = 0; index < objects.size(); index++)
            {
                var object = objects.getLong(index);

                if (sloInstance.equalsIgnoreCase(NMSConstants.InstanceType.MONITOR.getName()))
                {
                    var objectStatus = ObjectStatusCacheStore.getStore().getItem(object);

                    var status = getInitialSLOStatus(availabilitySLO, objectStatus);

                    if (status == SLOConstants.SLOFlapStatus.DEGRADED && globalSLOStatus == SLOConstants.SLOFlapStatus.HEALTHY)
                    {
                        globalSLOStatus = SLOConstants.SLOFlapStatus.DEGRADED;
                    }

                    var data = new JsonObject()
                            .put(SLOConstants.DURATION_LEFT, duration);

                    detail.put(object.toString(), data);

                    // add initial status as up or down
                    updateSLO(data, status, startTime, object, EMPTY_VALUE, sloId, cycleId);

                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug(String.format("object %s added to slo details for slo profile %s with data %s", object, sloId, data));
                    }
                }
                else
                {
                    var instances = MetricConfigStore.getStore().getInstanceObjectsByPluginNames(object, sloContext.getJsonArray(VisualizationConstants.PLUGINS));

                    for (var instanceObject : instances)
                    {
                        if (filter(instanceObject, sloContext.getJsonObject(FILTERS).getJsonObject(DATA_FILTER, null)))
                        {
                            var instance = sloInstance.equalsIgnoreCase(NMSConstants.InstanceType.INTERFACE.getName()) ? instanceObject.getString(NMSConstants.INTERFACE) : instanceObject.getString(AIOpsObject.OBJECT_NAME);

                            var instanceStatus = ObjectStatusCacheStore.getStore().getItem(object, instance);

                            var status = getInitialSLOStatus(availabilitySLO, instanceStatus);

                            if (status == SLOConstants.SLOFlapStatus.DEGRADED && globalSLOStatus == SLOConstants.SLOFlapStatus.HEALTHY)
                            {
                                globalSLOStatus = SLOConstants.SLOFlapStatus.DEGRADED;
                            }

                            var data = new JsonObject()
                                    .put(SLOConstants.DURATION_LEFT, duration);

                            detail.put(object + INSTANCE_SEPARATOR + instance, data);

                            // add initial status as up or down
                            updateSLO(data, status, startTime, object, instance, sloId, cycleId);

                            LOGGER.info(String.format("instance %s of object %s added to slo details for slo profile %s with data %s", instance, object, sloId, data));
                        }
                    }
                }
            }

            // add global slo
            var data = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, duration);

            sloDetails.get(sloId).put(EMPTY_VALUE, data);

            updateSLO(data, globalSLOStatus, startTime, NOT_AVAILABLE, EMPTY_VALUE, sloId, cycleId);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private boolean filter(JsonObject entry, JsonObject filter)
    {
        var result = false;

        try
        {
            if (filter != null && !filter.isEmpty())
            {
                var conditionGroup = filter.getJsonArray(CONDITION_GROUPS).getJsonObject(0);

                var operator = conditionGroup.getString(OPERATOR);

                var conditions = conditionGroup.getJsonArray(CONDITIONS);

                for (var j = 0; j < conditions.size(); j++)
                {
                    var condition = conditions.getJsonObject(j);

                    var operand =  condition.getString(OPERAND, EMPTY_VALUE);

                    if (entry.containsKey(operand))
                    {
                        result = PolicyEngineConstants.evaluateCondition(conditionGroup.getString(FILTER).equalsIgnoreCase("include"), condition.getString(OPERATOR), condition.getValue(VALUE), entry.getValue(operand));

                        if ((result && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName())) || (!result && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName())))
                        {
                            break;
                        }
                    }
                }
            }
            else
            {
                return true;
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    private SLOConstants.SLOFlapStatus getInitialSLOStatus(boolean availabilitySLO, String status)
    {
        return availabilitySLO && status != null ?
                status.equalsIgnoreCase(STATUS_UP) ?
                        SLOConstants.SLOFlapStatus.HEALTHY :
                        status.equalsIgnoreCase(STATUS_DISABLE) || status.equalsIgnoreCase(STATUS_MAINTENANCE) ?
                                SLOConstants.SLOFlapStatus.NOT_CALCULATED :
                                SLOConstants.SLOFlapStatus.DEGRADED
                : SLOConstants.SLOFlapStatus.HEALTHY;
    }

    // ------------------------ SLO calculation related methods ------------------------ //

    private void calculate()
    {
        // get the current timestamp
        var timestamp = DateTimeUtil.currentSeconds();

        for (var entry : sloDetails.entrySet())
        {
            try
            {
                var sloId = entry.getKey();

                var sloInstances = entry.getValue();

                // get the cycle id
                var cycleId = sloCycles.get(sloId);

                // get the slo profile
                var sloProfile = SLOProfileConfigStore.getStore().getItem(sloId);

                // get the slo cycle
                var sloCycle = SLOCycleConfigStore.getStore().getItem(cycleId);

                // get the slo context
                var sloContext = sloProfile.getJsonObject(SLOProfile.SLO_PROFILE_CONTEXT);

                // get the duration i.e. current timestamp - cycle start time
                var duration = timestamp - sloCycle.getLong(SLOCycle.SLO_CYCLE_START_TIME);

                // for daily slo dump data every 15 minute, for weekly 30 min, for monthly and quarterly 1 hour
                if ((SLOConstants.SLOFrequency.DAILY.getName().equalsIgnoreCase(sloContext.getString(SLOProfile.SLO_FREQUENCY))) ||
                        (SLOConstants.SLOFrequency.WEEKLY.getName().equalsIgnoreCase(sloContext.getString(SLOProfile.SLO_FREQUENCY)) && count.get() % 2 == 0) ||
                        (SLOConstants.SLOFrequency.MONTHLY.getName().equalsIgnoreCase(sloContext.getString(SLOProfile.SLO_FREQUENCY)) && count.get() % 4 == 0) ||
                        (SLOConstants.SLOFrequency.QUARTERLY.getName().equalsIgnoreCase(sloContext.getString(SLOProfile.SLO_FREQUENCY)) && count.get() % 4 == 0))
                {
                    int downCount = calculateInstanceSLO(sloInstances, sloId, sloCycle, sloContext, timestamp, duration, sloProfile.getString(SLOProfile.SLO_PROFILE_TYPE, EMPTY_VALUE).equalsIgnoreCase(SLOConstants.SLOType.AVAILABILITY.getName()));

                    calculateGlobalSLO(sloId, sloCycle, sloContext, timestamp, duration, downCount, sloProfile.getString(SLOProfile.SLO_PROFILE_TYPE, EMPTY_VALUE).equalsIgnoreCase(SLOConstants.SLOType.AVAILABILITY.getName()));
                }

                // reset the count once we reach 3
                if (count.getAndIncrement() > 4)
                {
                    count.set(1);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }
    }

    private int calculateInstanceSLO(Map<String, JsonObject> sloInstances, long sloId, JsonObject sloCycle, JsonObject sloContext, long timestamp, long duration, boolean availability)
    {
        // keep the count of down events
        var downCount = new AtomicInteger(0);

        var cycleId = sloCycle.getLong(ID);

        for (var entry : sloInstances.entrySet())
        {
            try
            {
                var key = entry.getKey();

                var value = entry.getValue();

                // empty value will be for global slo
                if (!key.equalsIgnoreCase(EMPTY_VALUE))
                {
                    var tokens = key.split(INSTANCE_SEPARATOR);

                    var objectId = CommonUtil.getLong(tokens[0]);

                    var instance = tokens.length > 1 ? tokens[1] : EMPTY_VALUE;

                    // update the last status
                    updateSLO(value, getLastSLOStatus(value), timestamp, objectId, instance, sloId, cycleId, true);

                    // if last status is breached than we need to update the downtime
                    if (getLastSLOStatus(value) == SLOConstants.SLOFlapStatus.DEGRADED)
                    {
                        // close the flap at current timestamp
                        sloEvents.get(sloId).merge(timestamp, -1, Integer::sum);

                        downCount.incrementAndGet();
                    }

                    write(ObjectConfigStore.getStore().getObjectId(objectId),
                            instance,
                            DatastoreConstants.DatastoreType.SLO_METRIC,
                            DatastoreConstants.PluginId.SLO_INSTANCE,
                            sloId + GROUP_SEPARATOR + cycleId,
                            VisualizationConstants.VisualizationDataSource.SLO_INSTANCE.getName(),
                            timestamp,
                            buildSLOCalculationEvent(value, sloCycle, sloContext.getInteger(SLOProfile.SLO_TARGET), sloContext.getInteger(SLOProfile.SLO_WARNING), instance, duration, availability));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }

        if (downCount.get() > 0)
        {
            sloEvents.get(sloId).put(timestamp, downCount.intValue());
        }

        return downCount.intValue();
    }

    private void calculateGlobalSLO(long sloId, JsonObject sloCycle, JsonObject sloContext, long timestamp, long duration, int downCount, boolean availability)
    {
        var cycleId = sloCycle.getLong(ID);

        // generate and dump data for global slo
        var result = merge(sloEvents.get(sloId), timestamp);

        sloEvents.get(sloId).clear();

        sloEvents.get(sloId).put(timestamp, downCount);

        var value = sloDetails.get(sloId).get(EMPTY_VALUE);

        // means any down flap is happen in this time interval
        if (!result.isEmpty())
        {
            for (var index = 0; index < result.size(); index++)
            {
                var event = result.getJsonObject(index);

                updateSLO(value, SLOConstants.SLOFlapStatus.values()[event.getInteger(SLOConstants.SLO_STATUS_FLAP)], event.getLong(TIME_STAMP), NOT_AVAILABLE, EMPTY_VALUE, sloId, cycleId);
            }

            // update the last flap
            updateSLO(value, getLastSLOStatus(value), timestamp, NOT_AVAILABLE, EMPTY_VALUE, sloId, cycleId, true);
        }
        // no down flap is there in this time frame
        else
        {
            updateSLO(value, SLOConstants.SLOFlapStatus.HEALTHY, timestamp, NOT_AVAILABLE, EMPTY_VALUE, sloId, cycleId, true);
        }

        write(sloId,
                cycleId + EMPTY_VALUE,
                DatastoreConstants.DatastoreType.SLO_METRIC,
                DatastoreConstants.PluginId.SLO,
                EMPTY_VALUE,
                VisualizationConstants.VisualizationDataSource.SLO.getName(),
                timestamp,
                buildGlobalSLOCalculationEvent(value, sloCycle, sloContext.getInteger(SLOProfile.SLO_TARGET), sloContext.getInteger(SLOProfile.SLO_WARNING), cycleId + EMPTY_VALUE, duration, availability));
    }

    // ------------------------ global SLO merging related methods ------------------------ //

    private JsonArray merge(TreeMap<Long, Integer> events, long endTime)
    {
        var result = new JsonArray();

        try
        {
            if (!events.isEmpty())
            {
                int active = 0;

                long lastTimestamp = events.firstKey();

                for (var event : events.entrySet())
                {
                    long timestamp = event.getKey();

                    if (timestamp > lastTimestamp)
                    {
                        append(result, lastTimestamp, active > 0 ? SLOConstants.SLOFlapStatus.DEGRADED.ordinal() : SLOConstants.SLOFlapStatus.HEALTHY.ordinal(), timestamp - lastTimestamp);
                    }

                    active += event.getValue();

                    lastTimestamp = timestamp;
                }

                // Add final interval from last event to endTime
                if (endTime > lastTimestamp)
                {
                    append(result, lastTimestamp, active > 0 ? SLOConstants.SLOFlapStatus.DEGRADED.ordinal() : SLOConstants.SLOFlapStatus.HEALTHY.ordinal(), endTime - lastTimestamp);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    private void append(JsonArray result, long timestamp, int sloStatus, long duration)
    {
        var valid = false;

        if (!result.isEmpty())
        {
            var lastFlap = result.getJsonObject(result.size() - 1);

            if (lastFlap.getInteger(SLOConstants.SLO_STATUS_FLAP) == sloStatus)
            {
                var newDuration = lastFlap.getLong(SLOConstants.SLO_DURATION) + duration;

                lastFlap.put(SLOConstants.SLO_DURATION, newDuration);

                valid = true;
            }
        }

        if (!valid)
        {
            result.add(new JsonObject()
                    .put(TIME_STAMP, timestamp)
                    .put(SLOConstants.SLO_STATUS_FLAP, sloStatus)
                    .put(SLOConstants.SLO_DURATION, duration));
        }
    }

    // ------------------------ SLO processing related methods ------------------------ //

    /**
     * This method is used to process the inspected result.
     * <p>
     *     We update the SLO based on the current status and timestamp.
     * </p>
     * @param inspectedResult the inspected result received from the {@link com.mindarray.policy.MetricPolicyInspector}
     */
    private void process(JsonObject inspectedResult)
    {
        try
        {
            var severity = inspectedResult.getString(SEVERITY);

            var object = inspectedResult.getLong(AIOpsConstants.ENTITY_ID);

            var instance = inspectedResult.getString(INSTANCE) == null ? EMPTY_VALUE : inspectedResult.getString(INSTANCE);

            var timestamp = inspectedResult.getLong(EventBusConstants.EVENT_TIMESTAMP);

            var sloId = inspectedResult.getLong(ID);

            var key = object + (!instance.isEmpty() ? INSTANCE_SEPARATOR + instance : EMPTY_VALUE);


            // performance SLO, event received from MetricPolicyInspector
            if (sloId != null)
            {
                var detail = sloDetails.get(sloId).get(key);

                // ignore the values if it's not added at the time of cycle start
                if (detail != null)
                {
                    if (severity.equalsIgnoreCase(Severity.CRITICAL.name()))
                    {
                        updateSLO(detail, SLOConstants.SLOFlapStatus.DEGRADED, timestamp, object, instance, sloId, sloCycles.get(sloId));
                    }
                    else
                    {
                        updateSLO(detail, SLOConstants.SLOFlapStatus.HEALTHY, timestamp, object, instance, sloId, sloCycles.get(sloId));
                    }
                }
            }
            // availability SLO, event received from ObjectStatusCacheStore
            else
            {
                availabilitySLOProfiles.forEach(id ->
                {
                    var detail = sloDetails.get(id).get(key);

                    if (detail != null && !severity.equalsIgnoreCase(STATUS_UNKNOWN))
                    {
                        if (severity.equalsIgnoreCase(STATUS_DOWN) || severity.equalsIgnoreCase(STATUS_UNREACHABLE))
                        {
                            updateSLO(detail, SLOConstants.SLOFlapStatus.DEGRADED, timestamp, object, instance, id, sloCycles.get(id));
                        }
                        else if (severity.equalsIgnoreCase(STATUS_DISABLE) || severity.equalsIgnoreCase(STATUS_MAINTENANCE))
                        {
                            updateSLO(detail, SLOConstants.SLOFlapStatus.NOT_CALCULATED, timestamp, object, instance, id, sloCycles.get(id));
                        }
                        else
                        {
                            updateSLO(detail, SLOConstants.SLOFlapStatus.HEALTHY, timestamp, object, instance, id, sloCycles.get(id));
                        }
                    }
                });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    // ------------------------ Utility methods ------------------------ //

    private void updateSLO(JsonObject data, SLOConstants.SLOFlapStatus currentStatus, long currentTimestamp, long objectId, String instance, long sloId, long cycleId)
    {
        updateSLO(data, currentStatus, currentTimestamp, objectId, instance, sloId, cycleId, false);
    }

    /**
     * This method updates the slo details based on the current status and timestamp.
     *
     * @param data            metadata of particular slo profile and particular monitor or instance
     * @param currentStatus   current status
     * @param currentTimestamp current timestamp
     * @param objectId        object id
     * @param instance        instance
     * @param sloId           slo id
     * @param cycleId         cycle id
     */
    private void updateSLO(JsonObject data, SLOConstants.SLOFlapStatus currentStatus, long currentTimestamp, long objectId, String instance, long sloId, long cycleId, boolean manualFlap)
    {
        try
        {
            SLOConstants.SLOFlapStatus lastSLOStatus;
            var lastTimeStamp = data.getLong(SLOConstants.LAST_TIMESTAMP);
            var lastBreachedTimestamp = data.getLong(SLOConstants.LAST_BREACHED_TIMESTAMP);
            var durationLeft = data.getLong(SLOConstants.DURATION_LEFT);
            var downIncidentCount = data.getInteger(SLOConstants.DOWN_INCIDENT_COUNT, 0);
            var upIncidentCount = data.getInteger(SLOConstants.UP_INCIDENT_COUNT, 0);

            // means first poll received
            if (data.getInteger(SLOConstants.LAST_SLO_STATUS) == null)
            {
                lastSLOStatus = currentStatus;
                lastTimeStamp = currentTimestamp;

                // if the current status is breached then update the breached timestamp`
                if (currentStatus == SLOConstants.SLOFlapStatus.DEGRADED)
                {
                    lastBreachedTimestamp = currentTimestamp;

                    // sloEvents are only for slo instances not for global slo
                    if (objectId != NOT_AVAILABLE)
                    {
                        sloEvents.get(sloId).merge(currentTimestamp, +1, Integer::sum);
                    }

                    downIncidentCount++;
                }
                else
                {
                    upIncidentCount++;
                }
            }
            else
            {
                lastSLOStatus = SLOConstants.SLOFlapStatus.values()[data.getInteger(SLOConstants.LAST_SLO_STATUS)];

                // in case of breached status will continuously minus the left duration
                if (currentStatus == SLOConstants.SLOFlapStatus.DEGRADED)
                {
                    if (lastSLOStatus == SLOConstants.SLOFlapStatus.DEGRADED)
                    {
                        durationLeft -= currentTimestamp - lastBreachedTimestamp;
                    }

                    lastBreachedTimestamp = currentTimestamp;
                }
                // if last status is breached and current status is ok than we've to minus the breached duration from the left duration
                else if (currentStatus == SLOConstants.SLOFlapStatus.HEALTHY && lastSLOStatus == SLOConstants.SLOFlapStatus.DEGRADED)
                {
                    // we've to minus the breached duration from the left duration
                    durationLeft -= currentTimestamp - lastBreachedTimestamp;
                }
                // if last status is breached and current status is not calculated (in case of disable or maintenance) than we've to minus the breached duration from the left duration
                else if (currentStatus == SLOConstants.SLOFlapStatus.NOT_CALCULATED && lastSLOStatus == SLOConstants.SLOFlapStatus.DEGRADED)
                {
                    // we've to minus the breached duration from the left duration
                    durationLeft -= currentTimestamp - lastBreachedTimestamp;
                }

                // if current status and last status is not matched then it's a flap
                if (lastSLOStatus != currentStatus || manualFlap)
                {
                    var duration = currentTimestamp - lastTimeStamp;

                    // this case happens when we dump manual flap and at the same time actual flap occurs
                    if (duration > 0)
                    {
                        // flap for slo instances
                        if (objectId != NOT_AVAILABLE)
                        {
                            write(ObjectConfigStore.getStore().getObjectId(objectId),
                                    instance,
                                    DatastoreConstants.DatastoreType.SLO_FLAP_METRIC,
                                    DatastoreConstants.PluginId.SLO_INSTANCE_FLAP,
                                    sloId + GROUP_SEPARATOR + cycleId,
                                    VisualizationConstants.VisualizationDataSource.SLO_INSTANCE_FLAP.getName(),
                                    lastTimeStamp,
                                    buildSLOFlapEvent(lastSLOStatus.ordinal(), duration, instance));
                        }
                        // flap for global slo
                        else
                        {
                            write(sloId,
                                    cycleId + EMPTY_VALUE,
                                    DatastoreConstants.DatastoreType.SLO_FLAP_METRIC,
                                    DatastoreConstants.PluginId.SLO_FLAP,
                                    EMPTY_VALUE,
                                    VisualizationConstants.VisualizationDataSource.SLO_FLAP.getName(),
                                    lastTimeStamp,
                                    buildGlobalSLOFlapEvent(lastSLOStatus.ordinal(), duration, cycleId + EMPTY_VALUE));
                        }
                    }

                    // don't update these details when manually dumping flap into the DB.
                    if (!manualFlap)
                    {
                        // update the respective incident count
                        if (currentStatus == SLOConstants.SLOFlapStatus.DEGRADED)
                        {
                            downIncidentCount++;
                        }
                        else
                        {
                            upIncidentCount++;
                        }

                        // sloEvents are only for slo instances not for global slo
                        if (objectId != NOT_AVAILABLE)
                        {
                            sloEvents.get(sloId).merge(currentTimestamp, currentStatus == SLOConstants.SLOFlapStatus.DEGRADED ? +1 : -1, Integer::sum);
                        }
                    }

                    lastSLOStatus = currentStatus;
                    lastTimeStamp = currentTimestamp;
                }
            }

            data.put(SLOConstants.LAST_SLO_STATUS, lastSLOStatus.ordinal())
                    .put(SLOConstants.LAST_TIMESTAMP, lastTimeStamp)
                    .put(SLOConstants.LAST_BREACHED_TIMESTAMP, lastBreachedTimestamp)
                    .put(SLOConstants.DURATION_LEFT, durationLeft)
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, downIncidentCount)
                    .put(SLOConstants.UP_INCIDENT_COUNT, upIncidentCount);

            updated = true;
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     *
     * @param data : metadata of particular slo profile and particular monitor or instance
     * @return achieved percentage i.e. duration left * 100 / total duration
     */
    private float getSLOAchievedPercentage(JsonObject data, JsonObject cycle)
    {
        return (data.getLong(SLOConstants.DURATION_LEFT) * 100) / (float) cycle.getLong(SLOCycle.SLO_CYCLE_TOTAL_DURATION);
    }

    /**
     *
     * @param data : metadata of particular slo profile and particular monitor or instance
     * @return violated seconds i.e. total duration - duration left
     */
    private long getSLOViolatedSeconds(JsonObject data, JsonObject cycle)
    {
        return cycle.getLong(SLOCycle.SLO_CYCLE_TOTAL_DURATION) - data.getLong(SLOConstants.DURATION_LEFT);
    }

    /**
     *
     * @param data : metadata of particular slo profile and particular monitor or instance
     * @return meantime to resolve i.e. violated seconds / down incident count
     */
    private long getSLOMeanTimeToResolve(JsonObject data, JsonObject cycle)
    {
        if (data.containsKey(SLOConstants.DOWN_INCIDENT_COUNT) && data.getInteger(SLOConstants.DOWN_INCIDENT_COUNT) > 0)
        {
            return getSLOViolatedSeconds(data, cycle) / data.getInteger(SLOConstants.DOWN_INCIDENT_COUNT);
        }
        else
        {
            return 0;
        }
    }

    /**
     *
     * @param data : metadata of particular slo profile and particular monitor or instance
     * @param duration : total duration of the slo cycle
     * @return meantime between failure i.e. (total duration - violated seconds) / up incident count
     */
    private long getSLOMeanTimeBetweenFailure(JsonObject data, JsonObject cycle, long duration)
    {
        if (data.containsKey(SLOConstants.UP_INCIDENT_COUNT) && data.getInteger(SLOConstants.UP_INCIDENT_COUNT) > 0)
        {
            return (duration - getSLOViolatedSeconds(data, cycle)) / data.getInteger(SLOConstants.UP_INCIDENT_COUNT);
        }
        else
        {
            return 0;
        }
    }

    /**
     *
     * @param data : metadata of particular slo profile and particular monitor or instance
     * @return violated percentage i.e. 100 - {@link #getSLOAchievedPercentage(JsonObject, JsonObject)}
     */
    private float getSLOViolatedPercentage(JsonObject data, JsonObject cycle)
    {
        return 100 - getSLOAchievedPercentage(data, cycle);
    }

    /**
     *
     * @param data : metadata of particular slo profile and particular monitor or instance
     * @param target : target of slo profile
     * @param warning : warning of slo profile
     * @return status of slo based on achieved percentage
     */
    private int getSLOStatus(JsonObject data, JsonObject cycle, int target, int warning)
    {
        var achieved = getSLOAchievedPercentage(data, cycle);

        if (achieved < target)
        {
            return SLOConstants.SLOStatus.BREACHED.ordinal();
        }
        else if (achieved < warning)
        {
            return SLOConstants.SLOStatus.WARNING.ordinal();
        }
        else
        {
            return SLOConstants.SLOStatus.OK.ordinal();
        }
    }

    /**
     *
     * @param data : metadata of particular slo profile and particular monitor or instance
     * @return last slo status
     */
    private SLOConstants.SLOFlapStatus getLastSLOStatus(JsonObject data)
    {
        return SLOConstants.SLOFlapStatus.values()[data.getInteger(SLOConstants.LAST_SLO_STATUS, 0)];
    }

    /**
     *
     * @param data : metadata of particular slo profile and particular monitor or instance
     * @param cycle : slo cycle details
     * @return error budget left percentage
     */
    private float getSLOErrorBudgetLeftPercentage(JsonObject data, JsonObject cycle)
    {
        return 100 - (((cycle.getLong(SLOCycle.SLO_CYCLE_TOTAL_DURATION) - data.getLong(SLOConstants.DURATION_LEFT)) * 100) / (float) cycle.getLong(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME));
    }

    /**
     *
     * @param data : metadata of particular slo profile and particular monitor or instance
     * @param cycle : slo cycle details
     * @return error budget left seconds
     */
    private long getSLOErrorBudgetLeftSeconds(JsonObject data, JsonObject cycle)
    {
        return cycle.getLong(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME) - (cycle.getLong(SLOCycle.SLO_CYCLE_TOTAL_DURATION) - data.getLong(SLOConstants.DURATION_LEFT));
    }

    private long getSLOBurnRateSeconds(JsonObject data, JsonObject cycle)
    {
        var violated = getSLOViolatedSeconds(data, cycle);

        var used = violated - data.getLong(SLOConstants.LAST_VIOLATED_SECONDS, 0L);

        data.put(SLOConstants.LAST_VIOLATED_SECONDS, violated);

        return used;
    }

    private float getSLOBurnRate(long burnRateSeconds, JsonObject cycle)
    {
        return (burnRateSeconds * 100) / (float) cycle.getLong(SLOCycle.SLO_CYCLE_TOTAL_DURATION);
    }

    // ------------------------ Datastore related methods ------------------------ //

    private void write(long objectId, String instance, DatastoreConstants.DatastoreType datastoreType, DatastoreConstants.PluginId pluginId, String pluginIdSuffix, String name, long timestamp, JsonObject event)
    {
        try
        {
            System.out.println("time stamp " + timestamp + " event " + event);

            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("writing slo event to datastore for object id %s and instance %s with datastore type %s and event %s", objectId, instance, datastoreType.name(), event.encode()));
            }

            var buffer = Buffer.buffer("UTF-8");

            appendBytes(buffer, pluginId, datastoreType, pluginIdSuffix, name, timestamp);

            if (name.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.SLO_INSTANCE.getName())
                    || name.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.SLO_INSTANCE_FLAP.getName()))
            {
                buffer.appendIntLE(CommonUtil.getInteger(objectId));
            }
            else
            {
                buffer.appendLongLE(objectId);
            }

            buffer.appendIntLE(instance.length());

            buffer.appendString(instance);

            for (var entry : event.getMap().entrySet())
            {
                if (entry.getValue() != null)
                {
                    var column = entry.getKey();

                    var value = CommonUtil.getString(entry.getValue());

                    var category = DatastoreConstants.getDataCategory(false, column, value);

                    buffer.appendByte(category);

                    var bytes = column.getBytes(StandardCharsets.UTF_8);

                    buffer.appendIntLE(bytes.length);

                    buffer.appendBytes(bytes);

                    if (category == DatastoreConstants.DataCategory.FLOAT.getName())
                    {
                        ByteUtil.writeDouble(buffer, CommonUtil.getDouble(value));
                    }
                    else if (category == DatastoreConstants.DataCategory.NUMERIC.getName())//if it has decimal number than convert it into integer
                    {
                        buffer.appendLongLE(Math.round(CommonUtil.getDouble(value)));
                    }
                    else
                    {
                        bytes = value.getBytes(StandardCharsets.UTF_8);

                        buffer.appendIntLE(bytes.length);

                        buffer.appendBytes(bytes);
                    }

                    // update metric columns
                    builder.setLength(0);

                    builder.append(category).append(COLUMN_SEPARATOR).append(pluginId.getName()).append(COLUMN_SEPARATOR).append(column).append(COLUMN_SEPARATOR).append(NO);

                    if (!mappers.contains(builder.toString()))
                    {
                        mappers.add(builder.toString());

                        vertx.eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                                new JsonObject()
                                        .put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_METRIC_COLUMN.name())
                                        .put(DatastoreConstants.MAPPER, builder.toString()));
                    }
                }
            }

            vertx.eventBus().send(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.METRIC.getName(), buffer.getBytes());
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void appendBytes(Buffer buffer, DatastoreConstants.PluginId pluginId, DatastoreConstants.DatastoreType datastoreType, String pluginIdSuffix, String name, long timestamp)
    {
        buffer.setLongLE(0, timestamp);

        var plugin = pluginId.getName() + DASH_SEPARATOR + name + (pluginIdSuffix.isEmpty() ? EMPTY_VALUE : DOT_SEPARATOR + pluginIdSuffix);

        var bytes = plugin.getBytes(StandardCharsets.UTF_8);

        buffer.appendIntLE(bytes.length);

        buffer.appendBytes(bytes);

        buffer.appendByte(DatastoreConstants.DatastoreFormat.VERTICAL.getName().byteValue());

        buffer.appendByte(CommonUtil.getByteValue(datastoreType.ordinal()));
    }

    private JsonObject buildGlobalSLOFlapEvent(int lastSLOStatus, long duration, String instance)
    {
        var event = new JsonObject();

        try
        {
            event.put(SLOConstants.SLO_STATUS_FLAP, lastSLOStatus)
                    .put(SLOConstants.SLO_DURATION, duration)
                    .put(SLOConstants.SLO_NAME, instance);

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return event;
    }

    private JsonObject buildSLOFlapEvent(int lastSLOStatus, long duration, String instance)
    {
        var event = new JsonObject();

        try
        {
            var isInstance = !instance.isEmpty();

            event.put(isInstance ? SLOConstants.SLO_INSTANCE_STATUS_FLAP : SLOConstants.SLO_OBJECT_STATUS_FLAP, lastSLOStatus)
                    .put(isInstance ? SLOConstants.SLO_INSTANCE_DURATION : SLOConstants.SLO_OBJECT_DURATION, duration);

            if (isInstance)
            {
                event.put(SLOConstants.SLO_INSTANCE_NAME, instance);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return event;
    }

    private JsonObject buildGlobalSLOCalculationEvent(JsonObject data, JsonObject sloCycle, int target, int warning, String instance, long duration, boolean availability)
    {
        var event = new JsonObject();

        try
        {
            var burnRateSeconds = getSLOBurnRateSeconds(data, sloCycle);

            event.put(SLOConstants.SLO_ACHIEVED_PERCENT, getSLOAchievedPercentage(data, sloCycle))
                    .put(SLOConstants.SLO_VIOLATED_PERCENT, getSLOViolatedPercentage(data, sloCycle))
                    .put(SLOConstants.SLO_VIOLATED_SECONDS, getSLOViolatedSeconds(data, sloCycle))
                    .put(SLOConstants.SLO_ERROR_BUDGET_LEFT_PERCENT, getSLOErrorBudgetLeftPercentage(data, sloCycle))
                    .put(SLOConstants.SLO_ERROR_BUDGET_LEFT_SECONDS, getSLOErrorBudgetLeftSeconds(data, sloCycle))
                    .put(SLOConstants.SLO_BURN_RATE_SECONDS, burnRateSeconds)
                    .put(SLOConstants.SLO_BURN_RATE_PERCENT, getSLOBurnRate(burnRateSeconds, sloCycle))
                    .put(SLOConstants.SLO_STATUS, getSLOStatus(data, sloCycle, target, warning))
                    .put(SLOConstants.SLO_NAME, instance);

            if (availability)
            {
                event.put(SLOConstants.SLO_MTTR_SECONDS, getSLOMeanTimeToResolve(data, sloCycle))
                    .put(SLOConstants.SLO_MTBF_SECONDS, getSLOMeanTimeBetweenFailure(data, sloCycle ,duration));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return event;
    }

    private JsonObject buildSLOCalculationEvent(JsonObject data, JsonObject sloCycle, int target, int warning, String instance, long duration, boolean availability)
    {
        var event = new JsonObject();

        try
        {
            var isInstance = !instance.isEmpty();

            var burnRateSeconds = getSLOBurnRateSeconds(data, sloCycle);

            event.put(isInstance ? SLOConstants.SLO_INSTANCE_ACHIEVED_PERCENT : SLOConstants.SLO_OBJECT_ACHIEVED_PERCENT, getSLOAchievedPercentage(data, sloCycle))
                    .put(isInstance ? SLOConstants.SLO_INSTANCE_VIOLATED_PERCENT : SLOConstants.SLO_OBJECT_VIOLATED_PERCENT, getSLOViolatedPercentage(data, sloCycle))
                    .put(isInstance ? SLOConstants.SLO_INSTANCE_VIOLATED_SECONDS : SLOConstants.SLO_OBJECT_VIOLATED_SECONDS, getSLOViolatedSeconds(data, sloCycle))
                    .put(isInstance ? SLOConstants.SLO_INSTANCE_ERROR_BUDGET_LEFT_PERCENT : SLOConstants.SLO_OBJECT_ERROR_BUDGET_LEFT_PERCENT, getSLOErrorBudgetLeftPercentage(data, sloCycle))
                    .put(isInstance ? SLOConstants.SLO_INSTANCE_ERROR_BUDGET_LEFT_SECONDS : SLOConstants.SLO_OBJECT_ERROR_BUDGET_LEFT_SECONDS, getSLOErrorBudgetLeftSeconds(data, sloCycle))
                    .put(isInstance ? SLOConstants.SLO_INSTANCE_BURN_RATE_SECONDS : SLOConstants.SLO_OBJECT_BURN_RATE_SECONDS, burnRateSeconds)
                    .put(isInstance ? SLOConstants.SLO_INSTANCE_BURN_RATE_PERCENT : SLOConstants.SLO_OBJECT_BURN_RATE_PERCENT, getSLOBurnRate(burnRateSeconds, sloCycle))
                    .put(isInstance ? SLOConstants.SLO_INSTANCE_STATUS : SLOConstants.SLO_OBJECT_STATUS, getSLOStatus(data, sloCycle, target, warning));

            if (availability)
            {
                event.put(isInstance ? SLOConstants.SLO_INSTANCE_MTTR_SECONDS : SLOConstants.SLO_OBJECT_MTTR_SECONDS, getSLOMeanTimeToResolve(data, sloCycle))
                    .put(isInstance ? SLOConstants.SLO_INSTANCE_MTBF_SECONDS : SLOConstants.SLO_OBJECT_MTBF_SECONDS, getSLOMeanTimeBetweenFailure(data, sloCycle, duration));
            }

            if (isInstance)
            {
                event.put(SLOConstants.SLO_INSTANCE_NAME, instance);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return event;
    }
}
