#!/bin/bash

#
#   Copyright (c) Motadata 2025. All rights reserved.
#
#   This source code is the property of Motadata and constitutes
#   proprietary and confidential information. Unauthorized copying, distribution,
#   modification, or use of this file, via any medium, is strictly prohibited
#   unless prior written permission is obtained from Motadata.
#
#   Unauthorized access or use of this software may result in legal action
#   and/or prosecution to the fullest extent of the law.
#
#   This software is provided "AS IS," without warranties of any kind, express
#   or implied, including but not limited to implied warranties of
#   merchantability or fitness for a particular purpose. In no event shall
#   Motadata be held liable for any damages arising from the use
#   of this software.
#
#   For inquiries, contact: <EMAIL>
#
#

DATE=$(date); echo "$DATE:executing manager upgrade script" > logs/MANAGER-UPGRADER-SCRIPT.log

UPGRADE_ME=$(cat upgrade.me)
echo "$UPGRADE_ME:fail" > upgrade.me

DATE=$(date);echo "$DATE:stopping motadata service" >> logs/MANAGER-UPGRADER-SCRIPT.log

service motadata stop
DATE=$(date);echo "$DATE:motadata service stopped" >> logs/MANAGER-UPGRADER-SCRIPT.log

PID=`ps -ef | grep -v grep | grep "motadata-manager"| awk '{print $2}'`

if [[ "" !=  "$PID" ]]; then
  DATE=$(date);echo "$DATE:motadata service is still alive, killing it forcefully" >> logs/MANAGER-UPGRADER-SCRIPT.log
  kill -9 $PID
fi

DATE=$(date);echo "$DATE:taking backup" >> logs/MANAGER-UPGRADER-SCRIPT.log
rm -rf backup/manager-artifacts/*
mkdir -p backup/manager-artifacts
cp -r lib motadata-manager backup/manager-artifacts/

DATE=$(date);echo "$DATE:updating manager artifacts" >> logs/MANAGER-UPGRADER-SCRIPT.log
rm -rf motadata-manager
cp downloads/manager-artifacts/linux/motadata-manager .

if [ -d "downloads/manager-artifacts/lib" ]; then
  	DATE=$(date);echo "$DATE:updating lib" >> logs/MANAGER-UPGRADER-SCRIPT.log
	  rm -rf lib ; cp -r downloads/manager-artifacts/lib .
fi

DATE=$(date);echo "$DATE:starting motadata service" >> logs/MANAGER-UPGRADER-SCRIPT.log
service motadata start

sleep 5

if (( $(ps -ef | grep -v grep | grep "motadata-manager" | wc -l) >= 1 ))
then
	DATE=$(date);echo "$DATE:motadata service started successfully" >> logs/MANAGER-UPGRADER-SCRIPT.log

	echo "$UPGRADE_ME:succeed" > upgrade.me
else
	DATE=$(date);echo "$DATE:motadata manager upgrade failed" >> logs/MANAGER-UPGRADER-SCRIPT.log
	service motadata stop

	ps -ef | grep -v grep | grep "motadata-manager"| awk '{print $2}' | xargs kill -9

	DATE=$(date);echo "$DATE:restoring manager artifacts" >> logs/MANAGER-UPGRADER-SCRIPT.log
	rm -rf motadata-manager lib
	cp -r backup/manager-artifacts/lib .
  cp backup/manager-artifacts/motadata-manager .

  DATE=$(date);echo "$DATE:starting motadata service" >> logs/MANAGER-UPGRADER-SCRIPT.log
	service motadata start
fi